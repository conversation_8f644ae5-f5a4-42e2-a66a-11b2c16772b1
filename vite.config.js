import {defineConfig, loadEnv} from 'vite'
import vue from '@vitejs/plugin-vue'
import AutoImport from 'unplugin-auto-import/vite'
import Components from 'unplugin-vue-components/vite'
import {ElementPlusResolver} from 'unplugin-vue-components/resolvers'
import { resolve } from 'path'

// https://vite.dev/config/
export default defineConfig(({ command, mode }) => {
  // 加载环境变量
  const env = loadEnv(mode, process.cwd(), '')

  return {
    plugins: [
        vue(),
        AutoImport({
            resolvers: [ElementPlusResolver()],
        }),
        Components({
            resolvers: [ElementPlusResolver()],
        }),
    ],
    resolve: {
        alias: {
            '@': resolve(__dirname, 'src')
        }
    },
    // 定义全局常量，可以在代码中使用
    define: {
        __APP_VERSION__: JSON.stringify(env.VITE_APP_VERSION || '1.0.0'),
        __APP_NAME__: JSON.stringify(env.VITE_APP_NAME || '知识库系统'),
    },
    server: {
        port: parseInt(env.VITE_DEV_PORT) || 3000,
        open: env.VITE_DEV_OPEN === 'true',
        host: true, // 允许外部访问
        // 开发代理配置
        proxy: command === 'serve' ? {
            // 将 /api 开头的请求代理到后端
            [env.VITE_BASE_API || '/api']: {
                target: env.VITE_BASE_URL,
                changeOrigin: true,
                secure: false,
                rewrite: (paths) => paths.replace(new RegExp('^' + env.VITE_BASE_API), ''),
                // configure: (proxy, options) => {
                //     // 代理配置回调
                //     proxy.on('error', (err, req, res) => {
                //         console.log('代理错误:', err);
                //     });
                //     proxy.on('proxyReq', (proxyReq, req, res) => {
                //         if (env.VITE_DEBUG === 'true') {
                //             console.log('发送请求到目标服务器:', req.method, req.url);
                //         }
                //     });
                //     proxy.on('proxyRes', (proxyRes, req, res) => {
                //         if (env.VITE_DEBUG === 'true') {
                //             console.log('从目标服务器接收响应:', proxyRes.statusCode, req.url);
                //         }
                //     });
                // }
            }
        } : {}
    },
    // 构建配置
    build: {
        // 生产环境移除console
        terserOptions: {
            compress: {
                drop_console: env.VITE_DEBUG !== 'true',
                drop_debugger: true,
            },
        },
        // 构建输出目录
        outDir: 'dist',
        // 静态资源目录
        assetsDir: 'assets',
        // 小于此阈值的导入或引用资源将内联为base64编码
        assetsInlineLimit: 4096,
    }
  }
})
