
import Word from "@/components/icon/word.vue"
import Excel from "@/components/icon/excel.vue"
import Pdf from "@/components/icon/pdf.vue"
import Ppt from "@/components/icon/ppt.vue"
import {h} from "vue"
export function getDocumentIcon(filename) {
	const extension = filename.split('.').pop().toLowerCase()
	switch (extension) {
		case 'pdf':
		return h(Pdf, {style: {color: '#e11d48',}})
		case 'doc':
		case 'docx':
		case 'word':
		return h(Word, {style: {color: '#0284c7'}})
		case 'xls':
		case 'xlsx':
		case 'excel':
		return h(Excel, {style: {color: '#059669'}})
		case 'ppt':
		case 'pptx':
		return h(Ppt, {style: {color: '#ea580c'}})
		case "email":
		return h(MailOutlined,{style:{color:'skyblue'}})
		default:
		return h(FileOutlined)
	}
}