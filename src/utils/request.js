import axios from 'axios'
import { ElMessage, ElMessageBox } from 'element-plus'
import { useUserStore } from '@/stores/user'
import router from '@/router'

// 环境变量配置
const config = {
  // API基础URL
  baseURL: import.meta.env.VITE_BASE_API || '/api',
  // 请求超时时间
  timeout: parseInt(import.meta.env.VITE_API_TIMEOUT) || 10000,
  // 是否开启调试模式
  debug: import.meta.env.VITE_DEBUG === 'true',
  // 应用信息
  appTitle: import.meta.env.VITE_APP_TITLE || '知识库系统',
  appVersion: import.meta.env.VITE_APP_VERSION || '1.0.0'
}

// 创建axios实例
const request = axios.create({
  baseURL: config.baseURL,
  timeout: config.timeout,
  // headers: {
  //   'Content-Type': 'application/json;charset=UTF-8',
  //   'X-App-Version': config.appVersion
  // }
})

// 请求拦截器
request.interceptors.request.use(
  (requestConfig) => {
    // 显示加载状态
    if (requestConfig.showLoading !== false && config.debug) {
      console.log('发起请求:', requestConfig.method?.toUpperCase(), requestConfig.url)
    }

    // 自动添加token
    const userStore = useUserStore()
    if (userStore.token) {
      requestConfig.headers.Authorization = `Bearer ${userStore.token}`
    }

    // 添加时间戳防止缓存
    // if (requestConfig.method === 'get') {
    //   requestConfig.params = {
    //     ...requestConfig.params,
    //     _t: Date.now()
    //   }
    // }

    // 添加环境信息到请求头
    // requestConfig.headers['X-Environment'] = import.meta.env.MODE
    // requestConfig.headers['X-App-Title'] = config.appTitle

    // 请求数据转换
    // if (requestConfig.data && requestConfig.headers['Content-Type']?.includes('application/json')) {
    //   requestConfig.data = JSON.stringify(requestConfig.data)
    // }

    return requestConfig
  },
  (error) => {
    console.error('请求拦截器错误:', error)
    ElMessage.error('请求配置错误')
    return Promise.reject(error)
  }
)

// 响应拦截器
request.interceptors.response.use(
  (response) => {
    // if (config.debug) {
    //   console.log('收到响应:', response.status, response.config.url)
    // }
    // console.log('响应数据:', response)

    const { data, status } = response

    // HTTP状态码检查
    if (status !== 200) {
      ElMessage.error(`请求失败: HTTP ${status}`)
      return Promise.reject(new Error(`HTTP ${status}`))
    }

    // 业务状态码检查
    if (data.code !== undefined) {
      // 假设后端返回格式为 { code: 0, message: 'success', data: {...} }
      if (data.code === 0 || data.code === 200) {
        // 成功响应
        return data
      } else {
        // 业务错误
        const errorMessage = data.message || '请求失败'
        ElMessage.error(errorMessage)
        return Promise.reject(new Error(errorMessage))
      }
    }

    // 如果没有code字段，直接返回data
    return data
  },
  (error) => {
    if (config.debug) {
      console.error('响应拦截器错误:', error)
    }

    // 网络错误处理
    if (!error.response) {
      ElMessage.error('网络连接失败，请检查网络设置')
      return Promise.reject(error)
    }

    const { status, data } = error.response

    // 根据HTTP状态码处理不同错误
    switch (status) {
      case 400:
        ElMessage.error(data?.message || '请求参数错误')
        break
      case 401:
        // token过期或无效
        handleTokenExpired()
        break
      case 403:
        ElMessage.error('没有权限访问该资源')
        break
      case 404:
        ElMessage.error('请求的资源不存在')
        break
      case 500:
        ElMessage.error('服务器内部错误')
        break
      case 502:
        ElMessage.error('网关错误')
        break
      case 503:
        ElMessage.error('服务暂时不可用')
        break
      default:
        ElMessage.error(data?.message || `请求失败: ${status}`)
    }

    return Promise.reject(error)
  }
)

// 处理token过期
const handleTokenExpired = async () => {
  const userStore = useUserStore()

  try {
    await ElMessageBox.confirm(
      '登录状态已过期，请重新登录',
      '登录过期',
      {
        confirmButtonText: '重新登录',
        cancelButtonText: '取消',
        type: 'warning',
        closeOnClickModal: false,
        closeOnPressEscape: false
      }
    )

    // 清除用户信息并跳转到登录页
    await userStore.logout()
    await router.push('/login')
  } catch {
    // 用户取消，也要清除token
    await userStore.logout()
    await router.push('/login')
  }
}

// 封装常用的请求方法
const http = {
  // GET请求
  get(url, params = {}, config = {}) {
    return request({
      method: 'get',
      url,
      params,
      ...config
    })
  },

  // POST请求
  post(url, data = {}, config = {}) {
    return request({
      method: 'post',
      url,
      data,
      ...config
    })
  },

  // PUT请求
  put(url, data = {}, config = {}) {
    return request({
      method: 'put',
      url,
      data,
      ...config
    })
  },

  // DELETE请求
  delete(url, params = {}, config = {}) {
    return request({
      method: 'delete',
      url,
      params,
      ...config
    })
  },

  // PATCH请求
  patch(url, data = {}, config = {}) {
    return request({
      method: 'patch',
      url,
      data,
      ...config
    })
  },

  // 文件上传
  upload(url, formData, config = {}) {
    return request({
      method: 'post',
      url,
      data: formData,
      headers: {
        'Content-Type': 'multipart/form-data'
      },
      ...config
    })
  },

  // 文件下载
  download(url, params = {}, config = {}) {
    return request({
      method: 'get',
      url,
      params,
      responseType: 'blob',
      ...config
    })
  }
}

// 请求状态管理
export const requestStatus = {
  // 当前进行中的请求数量
  pendingRequests: 0,

  // 增加请求计数
  addRequest() {
    this.pendingRequests++
  },

  // 减少请求计数
  removeRequest() {
    this.pendingRequests = Math.max(0, this.pendingRequests - 1)
  },

  // 是否有请求进行中
  get isLoading() {
    return this.pendingRequests > 0
  }
}

// 导出配置信息
export const requestConfig = {
  ...config,
  // 获取完整的API URL
  getFullApiUrl: (path = '') => {
    const baseUrl = import.meta.env.VITE_BASE_URL || ''
    const apiPath = config.baseURL
    return `${baseUrl}${apiPath}${path}`
  },
  // 检查是否为开发环境
  isDev: import.meta.env.DEV,
  // 检查是否为生产环境
  isProd: import.meta.env.PROD,
  // 获取当前环境模式
  mode: import.meta.env.MODE
}

// 导出
// export default http
export { request, config }
