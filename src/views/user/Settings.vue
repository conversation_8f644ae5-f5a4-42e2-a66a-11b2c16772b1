<template>
  <div class="settings-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><Setting /></el-icon>
            系统设置
          </h1>
          <p class="page-description">个性化配置和系统管理</p>
        </div>
      </div>
    </div>

    <!-- 设置内容 -->
    <div class="settings-content">
      <el-row :gutter="24">
        <!-- 左侧菜单 -->
        <el-col :span="6">
          <el-card shadow="never" class="settings-menu">
            <el-menu
              v-model:default-active="activeMenu"
              class="settings-menu-list"
              @select="handleMenuSelect"
            >
              <el-menu-item index="appearance">
                <el-icon><Sunny /></el-icon>
                <span>外观设置</span>
              </el-menu-item>
              <el-menu-item index="account">
                <el-icon><User /></el-icon>
                <span>账户设置</span>
              </el-menu-item>
              <el-menu-item index="notification">
                <el-icon><Bell /></el-icon>
                <span>通知设置</span>
              </el-menu-item>
              <el-menu-item index="security">
                <el-icon><Lock /></el-icon>
                <span>安全设置</span>
              </el-menu-item>
              <el-menu-item index="system">
                <el-icon><Tools /></el-icon>
                <span>系统设置</span>
              </el-menu-item>
              <el-menu-item index="about">
                <el-icon><InfoFilled /></el-icon>
                <span>关于系统</span>
              </el-menu-item>
            </el-menu>
          </el-card>
        </el-col>

        <!-- 右侧设置面板 -->
        <el-col :span="18">
          <el-card shadow="never" class="settings-panel">
            <!-- 外观设置 -->
            <div v-if="activeMenu === 'appearance'" class="setting-section">
              <h2 class="section-title">外观设置</h2>
              
              <div class="setting-item">
                <div class="item-info">
                  <h3>主题模式</h3>
                  <p>选择您喜欢的主题模式</p>
                </div>
                <div class="item-control">
                  <el-radio-group v-model="themeMode" @change="handleThemeChange">
                    <el-radio label="light">浅色主题</el-radio>
                    <el-radio label="dark">深色主题</el-radio>
                    <el-radio label="auto">跟随系统</el-radio>
                  </el-radio-group>
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>主题色彩</h3>
                  <p>自定义系统主色调</p>
                </div>
                <div class="item-control">
                  <el-color-picker v-model="primaryColor" @change="handleColorChange" />
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>字体大小</h3>
                  <p>调整界面字体大小</p>
                </div>
                <div class="item-control">
                  <el-select v-model="fontSize" placeholder="选择字体大小">
                    <el-option label="小" value="small" />
                    <el-option label="中" value="medium" />
                    <el-option label="大" value="large" />
                  </el-select>
                </div>
              </div>
            </div>

            <!-- 账户设置 -->
            <div v-if="activeMenu === 'account'" class="setting-section">
              <h2 class="section-title">账户设置</h2>
              
              <div class="setting-item">
                <div class="item-info">
                  <h3>个人信息</h3>
                  <p>管理您的个人资料</p>
                </div>
                <div class="item-control">
                  <el-form :model="userForm" label-width="80px">
                    <el-form-item label="用户名">
                      <el-input v-model="userForm.username" />
                    </el-form-item>
                    <el-form-item label="邮箱">
                      <el-input v-model="userForm.email" />
                    </el-form-item>
                    <el-form-item label="手机">
                      <el-input v-model="userForm.phone" />
                    </el-form-item>
                    <el-form-item>
                      <el-button type="primary">保存修改</el-button>
                    </el-form-item>
                  </el-form>
                </div>
              </div>
            </div>

            <!-- 通知设置 -->
            <div v-if="activeMenu === 'notification'" class="setting-section">
              <h2 class="section-title">通知设置</h2>
              
              <div class="setting-item">
                <div class="item-info">
                  <h3>邮件通知</h3>
                  <p>接收重要更新的邮件通知</p>
                </div>
                <div class="item-control">
                  <el-switch v-model="notifications.email" />
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>桌面通知</h3>
                  <p>在桌面显示系统通知</p>
                </div>
                <div class="item-control">
                  <el-switch v-model="notifications.desktop" />
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>声音提醒</h3>
                  <p>新消息时播放提示音</p>
                </div>
                <div class="item-control">
                  <el-switch v-model="notifications.sound" />
                </div>
              </div>
            </div>

            <!-- 安全设置 -->
            <div v-if="activeMenu === 'security'" class="setting-section">
              <h2 class="section-title">安全设置</h2>
              
              <div class="setting-item">
                <div class="item-info">
                  <h3>修改密码</h3>
                  <p>定期更新密码以保护账户安全</p>
                </div>
                <div class="item-control">
                  <el-button type="primary">修改密码</el-button>
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>两步验证</h3>
                  <p>启用两步验证增强账户安全</p>
                </div>
                <div class="item-control">
                  <el-switch v-model="security.twoFactor" />
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>登录历史</h3>
                  <p>查看最近的登录记录</p>
                </div>
                <div class="item-control">
                  <el-button>查看历史</el-button>
                </div>
              </div>
            </div>

            <!-- 系统设置 -->
            <div v-if="activeMenu === 'system'" class="setting-section">
              <h2 class="section-title">系统设置</h2>
              
              <div class="setting-item">
                <div class="item-info">
                  <h3>语言设置</h3>
                  <p>选择系统显示语言</p>
                </div>
                <div class="item-control">
                  <el-select v-model="systemSettings.language">
                    <el-option label="简体中文" value="zh-CN" />
                    <el-option label="English" value="en-US" />
                  </el-select>
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>自动保存</h3>
                  <p>编辑文档时自动保存</p>
                </div>
                <div class="item-control">
                  <el-switch v-model="systemSettings.autoSave" />
                </div>
              </div>

              <div class="setting-item">
                <div class="item-info">
                  <h3>数据备份</h3>
                  <p>定期备份重要数据</p>
                </div>
                <div class="item-control">
                  <el-button type="primary">立即备份</el-button>
                </div>
              </div>
            </div>

            <!-- 关于系统 -->
            <div v-if="activeMenu === 'about'" class="setting-section">
              <h2 class="section-title">关于系统</h2>
              
              <div class="about-content">
                <div class="system-info">
                  <h3>知识库管理系统</h3>
                  <p class="version">版本 v1.0.0</p>
                  <p class="description">
                    基于 Vue 3 + Element Plus 构建的现代化知识库管理系统，
                    提供文档管理、分类组织、智能搜索等功能。
                  </p>
                </div>
                
                <div class="tech-stack">
                  <h4>技术栈</h4>
                  <div class="tech-tags">
                    <el-tag>Vue 3</el-tag>
                    <el-tag>Element Plus</el-tag>
                    <el-tag>Vue Router</el-tag>
                    <el-tag>Pinia</el-tag>
                    <el-tag>Vite</el-tag>
                    <el-tag>SCSS</el-tag>
                  </div>
                </div>

                <div class="contact-info">
                  <h4>联系我们</h4>
                  <p>如有问题或建议，请联系技术支持团队</p>
                  <el-button type="primary">反馈问题</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useThemeStore } from '@/stores/theme'
import { 
  Setting, 
  Sunny, 
  User, 
  Bell, 
  Lock, 
  Tools, 
  InfoFilled 
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 状态管理
const themeStore = useThemeStore()

// 响应式数据
const activeMenu = ref('appearance')
const themeMode = ref(themeStore.isDark ? 'dark' : 'light')
const primaryColor = ref('#6366f1')
const fontSize = ref('medium')

// 用户表单
const userForm = reactive({
  username: '用户名',
  email: '<EMAIL>',
  phone: '138****8888'
})

// 通知设置
const notifications = reactive({
  email: true,
  desktop: false,
  sound: true
})

// 安全设置
const security = reactive({
  twoFactor: false
})

// 系统设置
const systemSettings = reactive({
  language: 'zh-CN',
  autoSave: true
})

// 方法
const handleMenuSelect = (key) => {
  activeMenu.value = key
}

const handleThemeChange = (value) => {
  if (value === 'light') {
    themeStore.setTheme('light')
  } else if (value === 'dark') {
    themeStore.setTheme('dark')
  } else {
    // 跟随系统
    const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
    themeStore.setTheme(prefersDark ? 'dark' : 'light')
  }
  ElMessage.success('主题设置已保存')
}

const handleColorChange = (color) => {
  ElMessage.info('主题色彩功能开发中...')
}

onMounted(() => {
  console.log('设置页面已加载')
})
</script>

<style lang="scss" scoped>
.settings-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.page-header {
  margin-bottom: 24px;
  
  .header-content {
    .header-left {
      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: var(--text-color-primary);
        
        .el-icon {
          font-size: 32px;
          color: var(--primary-color);
        }
      }
      
      .page-description {
        margin: 0;
        color: var(--text-color-secondary);
        font-size: 16px;
      }
    }
  }
}

.settings-content {
  .settings-menu {
    .settings-menu-list {
      border-right: none;
      
      .el-menu-item {
        margin-bottom: 4px;
        border-radius: 8px;
        
        &:hover {
          background-color: var(--fill-color);
        }
        
        &.is-active {
          background-color: var(--primary-light);
          color: var(--primary-color);
        }
      }
    }
  }
  
  .settings-panel {
    min-height: 600px;
    
    .setting-section {
      .section-title {
        margin: 0 0 24px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color-primary);
        padding-bottom: 12px;
        border-bottom: 1px solid var(--border-color);
      }
      
      .setting-item {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        padding: 20px 0;
        border-bottom: 1px solid var(--border-color-light);
        
        &:last-child {
          border-bottom: none;
        }
        
        .item-info {
          flex: 1;
          margin-right: 24px;
          
          h3 {
            margin: 0 0 4px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color-primary);
          }
          
          p {
            margin: 0;
            color: var(--text-color-secondary);
            font-size: 14px;
            line-height: 1.5;
          }
        }
        
        .item-control {
          flex-shrink: 0;
        }
      }
      
      .about-content {
        .system-info {
          margin-bottom: 32px;
          
          h3 {
            margin: 0 0 8px 0;
            font-size: 20px;
            font-weight: 600;
            color: var(--text-color-primary);
          }
          
          .version {
            margin: 0 0 16px 0;
            color: var(--primary-color);
            font-weight: 500;
          }
          
          .description {
            margin: 0;
            color: var(--text-color-regular);
            line-height: 1.6;
          }
        }
        
        .tech-stack {
          margin-bottom: 32px;
          
          h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color-primary);
          }
          
          .tech-tags {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
          }
        }
        
        .contact-info {
          h4 {
            margin: 0 0 12px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color-primary);
          }
          
          p {
            margin: 0 0 16px 0;
            color: var(--text-color-regular);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .settings-page {
    padding: 16px;
  }
  
  .settings-content {
    .el-col {
      margin-bottom: 16px;
    }
  }
  
  .setting-item {
    flex-direction: column;
    align-items: stretch;
    
    .item-info {
      margin-right: 0;
      margin-bottom: 16px;
    }
  }
}
</style>
