<template>
  <div class="login-page">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
      <div class="floating-shape shape-4"></div>
    </div>

    <!-- 登录容器 -->
    <div class="login-container">
      <!-- 左侧品牌区域 -->
      <div class="brand-section">
        <div class="brand-content">
          <div class="brand-logo">
            <el-icon class="logo-icon"><Collection /></el-icon>
          </div>
          <h1 class="brand-title">知识库系统</h1>
          <p class="brand-subtitle">现代化的知识管理平台</p>
          <div class="feature-list">
            <div class="feature-item">
              <el-icon><Document /></el-icon>
              <span>智能文档管理</span>
            </div>
            <div class="feature-item">
              <el-icon><FolderOpened /></el-icon>
              <span>分类体系完善</span>
            </div>
            <div class="feature-item">
              <el-icon><Search /></el-icon>
              <span>全文搜索功能</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="form-section">
        <div class="form-container">
          <!-- 表单头部 -->
          <div class="form-header">
            <h2 class="form-title">欢迎回来</h2>
            <p class="form-subtitle">请登录您的账户以继续使用</p>
          </div>

          <!-- 登录表单 -->
          <el-form
            ref="loginFormRef"
            :model="loginForm"
            :rules="loginRules"
            class="login-form"
            size="large"
            @submit.prevent="handleLogin"
          >
            <el-form-item prop="username">
              <el-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                :prefix-icon="User"
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item prop="password">
              <el-input
                v-model="loginForm.password"
                type="password"
                placeholder="请输入密码"
                :prefix-icon="Lock"
                show-password
                clearable
                @keyup.enter="handleLogin"
              />
            </el-form-item>

            <el-form-item>
              <div class="form-options">
                <el-checkbox v-model="rememberMe">记住我</el-checkbox>
                <el-button type="primary" link class="forgot-password">
                  忘记密码？
                </el-button>
              </div>
            </el-form-item>

            <el-form-item>
              <el-button
                type="primary"
                class="login-button"
                :loading="userStore.isLoading"
                @click="handleLogin"
              >
                <span v-if="!userStore.isLoading">登录</span>
                <span v-else>登录中...</span>
              </el-button>
            </el-form-item>
          </el-form>

          <!-- 演示账户 -->
          <div class="demo-accounts">
            <div class="demo-title">
              <el-divider>演示账户</el-divider>
            </div>
            <div class="demo-list">
              <div 
                v-for="account in demoAccounts" 
                :key="account.username"
                class="demo-account"
                @click="fillDemoAccount(account)"
              >
                <div class="demo-info">
                  <span class="demo-name">{{ account.name }}</span>
                  <span class="demo-role">{{ account.role === 'admin' ? '管理员' : '普通用户' }}</span>
                </div>
                <div class="demo-credentials">
                  <span>{{ account.username }} / {{ account.password }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 主题切换 -->
          <div class="theme-toggle">
            <el-button
              class="theme-toggle-btn"
              @click="themeStore.toggleTheme()"
            >
              <el-icon>
                <component :is="themeStore.isDark ? 'Sunny' : 'Moon'" />
              </el-icon>
            </el-button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import {
  Collection,
  Document,
  FolderOpened,
  Search,
  User,
  Lock,
  Sunny,
  Moon
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 路由和状态管理
const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const themeStore = useThemeStore()

// 表单引用
const loginFormRef = ref()

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

const rememberMe = ref(false)

// 表单验证规则
const loginRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 2, max: 20, message: '用户名长度在 2 到 20 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 20, message: '密码长度在 6 到 20 个字符', trigger: 'blur' }
  ]
}

// 演示账户
const demoAccounts = [
  { username: 'admin', password: '123456', name: '管理员', role: 'admin' },
  { username: 'user', password: '123456', name: '普通用户', role: 'user' },
  { username: 'demo', password: 'demo123', name: '演示用户', role: 'user' }
]

// 填充演示账户
const fillDemoAccount = (account) => {
  loginForm.username = account.username
  loginForm.password = account.password
  ElMessage.info(`已填充 ${account.name} 的登录信息`)
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    // 表单验证
    await loginFormRef.value.validate()
    
    // 执行登录
    await userStore.login({
      username: loginForm.username,
      password: loginForm.password
    })

    // 登录成功后跳转
    const redirect = route.query.redirect || '/'
    await router.push(redirect)
    
  } catch (error) {
    console.error('登录失败:', error)
  }
}

// 组件挂载时检查是否已登录
onMounted(() => {
  // 如果已经登录，直接跳转
  if (userStore.isLoggedIn) {
    const redirect = route.query.redirect || '/'
    router.push(redirect)
  }
})
</script>

<style lang="scss" scoped>
.login-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-color-page) 0%, var(--fill-color-light) 100%);
  position: relative;
  overflow: hidden;
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  
  .floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    opacity: 0.1;
    animation: float 8s ease-in-out infinite;
    
    &.shape-1 {
      width: 120px;
      height: 120px;
      top: 10%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.shape-2 {
      width: 80px;
      height: 80px;
      top: 20%;
      right: 20%;
      animation-delay: 2s;
    }
    
    &.shape-3 {
      width: 150px;
      height: 150px;
      bottom: 20%;
      left: 15%;
      animation-delay: 4s;
    }
    
    &.shape-4 {
      width: 100px;
      height: 100px;
      bottom: 30%;
      right: 10%;
      animation-delay: 6s;
    }
  }
}

.login-container {
  display: flex;
  width: 100%;
  max-width: 1000px;
  min-height: 500px;
  background: var(--card-bg);
  border-radius: 20px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

.brand-section {
  flex: 1;
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 60px 40px;
  color: white;
  
  .brand-content {
    text-align: center;
    
    .brand-logo {
      margin-bottom: 24px;
      
      .logo-icon {
        font-size: 64px;
        opacity: 0.9;
      }
    }
    
    .brand-title {
      font-size: 32px;
      font-weight: 700;
      margin: 0 0 12px 0;
      letter-spacing: -0.025em;
    }
    
    .brand-subtitle {
      font-size: 16px;
      opacity: 0.8;
      margin: 0 0 40px 0;
    }
    
    .feature-list {
      .feature-item {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 12px;
        margin-bottom: 16px;
        font-size: 14px;
        opacity: 0.9;
        
        .el-icon {
          font-size: 18px;
        }
      }
    }
  }
}

.form-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  // padding: 60px 40px;
  position: relative;
}

.form-container {
  width: 100%;
  max-width: 400px;
}

.form-header {
  text-align: center;
  margin:20px;
  
  .form-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--text-color-primary);
    margin: 0px 0 8px 0;
    letter-spacing: -0.025em;
  }
  
  .form-subtitle {
    color: var(--text-color-secondary);
    margin: 0;
    font-size: 14px;
  }
}

.login-form {
  .el-form-item {
    margin-bottom: 24px;
  }
  
  .form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    
    .forgot-password {
      padding: 10px;
      font-size: 14px;
    }
  }
  
  .login-button {
    width: 100%;
    height: 48px;
    font-size: 16px;
    font-weight: 600;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    
    &:hover {
      background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(99, 102, 241, 0.35);
    }
  }
}

.demo-accounts {
  margin-top: 32px;
  
  .demo-title {
    margin-bottom: 16px;
    
    .el-divider {
      font-size: 12px;
      color: var(--text-color-secondary);
    }
  }
  
  .demo-list {
    .demo-account {
      padding: 12px 16px;
      border: 1px solid var(--border-color);
      border-radius: 8px;
      margin-bottom: 8px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      
      &:hover {
        border-color: var(--primary-color);
        background-color: var(--primary-light);
      }
      
      .demo-info {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;
        
        .demo-name {
          font-weight: 500;
          color: var(--text-color-primary);
        }
        
        .demo-role {
          font-size: 12px;
          color: var(--text-color-secondary);
        }
      }
      
      .demo-credentials {
        font-size: 12px;
        color: var(--text-color-placeholder);
        font-family: 'Courier New', monospace;
      }
    }
  }
}

.theme-toggle {
  position: absolute;
  top: 20px;
  right: 20px;
  
  .theme-toggle-btn {
    width: 40px;
    height: 40px;
    border-radius: 12px;
    background: var(--fill-color);
    border: 1px solid var(--border-color);
    color: var(--text-color-secondary);
    display: flex;
    align-items: center;
    justify-content: center;
    
    &:hover {
      color: var(--primary-color);
      border-color: var(--primary-color);
      background: var(--primary-light);
    }
  }
}

// 动画
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .login-container {
    flex-direction: column;
    margin: 20px;
    min-height: auto;
  }
  
  .brand-section {
    padding: 40px 20px;
    
    .brand-content {
      .brand-logo .logo-icon {
        font-size: 48px;
      }
      
      .brand-title {
        font-size: 24px;
      }
      
      .feature-list {
        display: none;
      }
    }
  }
  
  .form-section {
    padding: 40px 20px;
  }
  
  .theme-toggle {
    position: static;
    text-align: center;
    margin-top: 20px;
  }
}
</style>
