<template>
  <div class="knowledge-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <el-icon><FolderOpened /></el-icon>
            知识管理
          </h1>
          <p class="page-description">统一管理您的文档和分类体系</p>
        </div>
        <div class="header-actions">
          <el-button type="primary" :icon="Plus" @click="createDocument">
            新建文档
          </el-button>
          <el-button :icon="FolderAdd" @click="createCategory">
            新建分类
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="24">
        <el-col :span="6">
          <el-card shadow="never" class="category-panel">
            <template #header>
              <div class="panel-header">
                <span>知识分类</span>
                <div class="header-actions">
                  <el-button size="small" text @click="refreshCategories">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </div>
              </div>
            </template>

            <!-- 分类列表 -->
            <div class="category-list" v-loading="isLoadingCategories">
              <div class="category-item all-category"
                   :class="{ active: !selectedCategory }"
                  >
                <div class="category-info">
                  <el-icon class="category-icon"><FolderOpened /></el-icon>
                  <span class="category-name">全部文档</span>
                </div>
                <el-tag size="small" type="info">{{ categoryTotal }}</el-tag>
              </div>

              <div v-for="category in categories"
                   :key="category.id"
                   class="category-item"
                   :class="{ active: selectedCategory?.id === category.id }"
                   @click="selectCategory(category)">
                <div class="category-info">
                  <el-icon class="category-icon">
                    <component :is="category.icon" />
                  </el-icon>
                  <div class="category-details">
                    <span class="category-name">{{ category.name }}</span>
                    <span class="category-desc">{{ category.description }}</span>
                  </div>
                </div>
                <div class="category-actions">
                  <el-tag size="small" type="primary">{{ category.doc_count }}</el-tag>
                  <el-button
                    size="small"
                    type="danger"
                    text
                    :icon="Delete"
                    class="delete-btn"
                    @click.stop="deleteCategory(category)"
                    title="删除分类"
                  />
                </div>
              </div>

              <!-- 空状态 -->
              <div v-if="!isLoadingCategories && categories.length === 0" class="empty-categories">
                <el-empty description="暂无知识分类" :image-size="80">
                  <el-button type="primary" @click="createCategory">创建分类</el-button>
                </el-empty>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧文档区域 -->
        <el-col :span="18">
          <el-card shadow="never" class="document-panel">
            <template #header>
              <div class="panel-header">
                <span>
                  {{ selectedCategory ? `${selectedCategory.name} 的文档` : '全部文档' }}
                  <el-tag size="small" type="info">{{ total }} 个</el-tag>
                </span>
                <div class="header-actions">

                </div>
              </div>
            </template>

            <!-- 文档列表 -->
            <div class="document-list">
              <!-- 批量操作工具栏 -->
              <div v-if="selectedDocuments.length > 0" class="batch-actions-toolbar">
                <div class="selected-info">
                  <el-icon><InfoFilled /></el-icon>
                  <span>已选择 {{ selectedDocuments.length }} 个文档</span>
                </div>
                <div class="batch-actions">
                  <el-dropdown @command="handleBatchAction" trigger="click">
                    <el-button type="primary" size="small">
                      批量操作
                      <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item command="parse">
                          <el-icon><DocumentCopy /></el-icon>
                          解析文档
                        </el-dropdown-item>
                        <el-dropdown-item command="delete" divided>
                          <el-icon><Delete /></el-icon>
                          删除文档
                        </el-dropdown-item>
                        <el-dropdown-item command="cancel" divided>
                          <el-icon><Close /></el-icon>
                          取消选择
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </div>

              <!-- 列表视图 -->
              <div class="list-view">
                <el-table
                  ref="documentTable"
                  :data="tableData"
                  style="width: 100%"
                  :max-height="tableMaxHeight"
                  @row-click="handleRowClick"
                  @selection-change="handleSelectionChange"
                  row-class-name="document-row"
                  :empty-text="emptyText"
                  highlight-current-row
                >
                  <!-- 选择列 -->
                  <el-table-column type="selection" width="55" align="center" />

                  <el-table-column width="60" align="center">
                    <template #default="{ row }">
                      <!-- <el-icon class="doc-icon">
                        <Document />
                      </el-icon> -->
                       <component :is="getDocumentIcon(row.file_type)"  />
                    </template>
                  </el-table-column>

                  <el-table-column prop="title" label="文档标题" min-width="250">
                    <template #default="{ row }">
                      <el-tooltip placement="top" :disabled="!row.title && !row.description" raw-content>
                        <template #content>
                          <div style="white-space: normal; word-wrap: break-word; max-width: 600px; line-height: 1.4;">
                            <div v-if="row.title" style="font-weight: 500; margin-bottom: 4px;">{{ row.title || '未命名文档' }}</div>
                            <div v-if="row.description" style="color: #909399; font-size: 12px;">{{ row.description }}</div>
                          </div>
                        </template>
                        <div class="title-cell">
                          <span class="title-text">{{ row.title || '未命名文档' }}</span>
                          <span class="title-desc">{{ row.description }}</span>
                        </div>
                      </el-tooltip>
                    </template>
                  </el-table-column>

                  <el-table-column prop="status" label="状态" width="100" align="center">
                    <template #default="{ row }">
                      <el-tooltip placement="top" :disabled="!row.processed_status" raw-content>
                        <template #content>
                          <div style="white-space: pre-line; max-width: 300px;">{{ row.processed_status }}</div>
                        </template>
                        <el-tag :type="getStatusType(row.status)" size="small" effect="light">
                          {{ getStatusName(row.status) }}
                        </el-tag>
                      </el-tooltip>
                    </template>
                  </el-table-column>

                  <el-table-column prop="author" label="作者" width="120" align="center">
                    <template #default="{ row }">
                      <div class="author-cell">
                        <el-avatar :size="24" class="author-avatar">
                          {{ (row.author || '未知')[0] }}
                        </el-avatar>
                        <span class="author-name">{{ row.author || '未知' }}</span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column prop="updateTime" label="更新时间" width="180" align="center">
                    <template #default="{ row }">
                      <div class="time-cell">
                        <el-icon class="time-icon"><Clock /></el-icon>
                        <span>{{ formatTime(row.updateTime) }}</span>
                      </div>
                    </template>
                  </el-table-column>

                  <el-table-column label="操作" width="200" fixed="right" align="center">
                    <template #default="{ row }">
                      <div class="action-buttons">
                        <el-button
                          size="small"
                          type="primary"
                          link
                          @click.stop="viewDocument(row)"
                          :icon="View"
                        >
                          查看
                        </el-button>
                        <el-button
                          size="small"
                          type="danger"
                          link
                          @click.stop="deleteDocument(row)"
                          :icon="Delete"
                        >
                          删除
                        </el-button>
                      </div>
                    </template>
                  </el-table-column>
                </el-table>
              </div>

              <!-- 分页组件 -->
              <div class="pagination-wrapper" v-if="total > 0">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :page-sizes="pageSizes"
                  :total="total"
                  :background="true"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  class="document-pagination"
                />
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 创建知识库弹窗 -->
    <el-dialog
      v-model="createKnowledgeDialogVisible"
      title="创建知识库"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="resetCreateKnowledgeForm"
    >
      <el-form
        ref="createKnowledgeFormRef"
        :model="createKnowledgeForm"
        :rules="createKnowledgeRules"
        label-width="100px"
        label-position="left"
      >
        <el-form-item label="知识库名称" prop="name">
          <el-input
            v-model="createKnowledgeForm.name"
            placeholder="请输入知识库名称"
            maxlength="50"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="知识库描述" prop="description">
          <el-input
            v-model="createKnowledgeForm.description"
            type="textarea"
            placeholder="请输入知识库描述（可选）"
            :rows="4"
            maxlength="200"
            show-word-limit
            clearable
          />
        </el-form-item>

        <el-form-item label="是否公开" prop="public">
          <el-switch
            v-model="createKnowledgeForm.public"
            active-text="公开"
            inactive-text="私有"
            active-color="var(--primary-color)"
            inactive-color="var(--text-color-placeholder)"
          />
          <div class="form-item-tip">
            <el-text size="small" type="info">
              公开的知识库可以被其他用户查看和搜索
            </el-text>
          </div>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancelCreateKnowledge" :disabled="isCreatingKnowledge">
            取消
          </el-button>
          <el-button
            type="primary"
            @click="confirmCreateKnowledge"
            :loading="isCreatingKnowledge"
            :disabled="isCreatingKnowledge"
          >
            {{ isCreatingKnowledge ? '创建中...' : '确定创建' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 新建文档弹窗 -->
    <el-dialog
      v-model="createDocumentDialogVisible"
      title="新建文档"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @closed="cancelUpload"
    >
      <!-- 步骤指示器 -->
      <div class="upload-steps">
        <div class="step-item" :class="{ active: uploadStep === 1, completed: uploadStep > 1 }">
          <div class="step-number">1</div>
          <div class="step-title">选择知识库</div>
        </div>
        <div class="step-line" :class="{ active: uploadStep > 1 }"></div>
        <div class="step-item" :class="{ active: uploadStep === 2 }">
          <div class="step-number">2</div>
          <div class="step-title">上传文件</div>
        </div>
      </div>

      <!-- 第一步：选择知识库 -->
      <div v-if="uploadStep === 1" class="step-content">
        <div class="step-description">
          <el-icon><Select /></el-icon>
          <span>请选择要上传文档的目标知识库</span>
        </div>

        <div class="knowledge-base-list">
          <div
            v-for="category in categories"
            :key="category.id"
            class="knowledge-base-item"
            :class="{ selected: selectedKnowledgeBase?.id === category.id }"
            @click="selectKnowledgeBase(category)"
          >
            <div class="knowledge-base-info">
              <el-icon class="knowledge-base-icon">
                <component :is="category.icon" />
              </el-icon>
              <div class="knowledge-base-details">
                <div class="knowledge-base-name">{{ category.name }}</div>
                <div class="knowledge-base-desc">{{ category.description }}</div>
              </div>
            </div>
            <div class="knowledge-base-meta">
              <el-tag size="small" type="info">{{ category.doc_count }} 个文档</el-tag>
              <el-icon v-if="selectedKnowledgeBase?.id === category.id" class="selected-icon">
                <Check />
              </el-icon>
            </div>
          </div>

          <!-- 空状态 -->
          <div v-if="categories.length === 0" class="empty-knowledge-bases">
            <el-empty description="暂无知识库" :image-size="80">
              <el-button type="primary" @click="createCategory">创建知识库</el-button>
            </el-empty>
          </div>
        </div>
      </div>

      <!-- 第二步：上传文件 -->
      <div v-if="uploadStep === 2" class="step-content">
        <div class="step-description">
          <el-icon><Upload /></el-icon>
          <span>选择要上传的文件（最多 {{ maxFileCount }} 个）</span>
        </div>

        <!-- 文件选择区域 -->
        <div class="file-upload-area">
          <input
            ref="fileInput"
            type="file"
            multiple
            accept=".pdf,.doc,.docx,.txt,.md,.ppt,.pptx,.xls,.xlsx"
            style="display: none"
            @change="handleFileSelect"
          />

          <div class="upload-dropzone" @click="$refs.fileInput?.click()">
            <el-icon class="upload-icon"><Upload /></el-icon>
            <div class="upload-text">
              <p>点击选择文件或拖拽文件到此处</p>
              <p class="upload-hint">支持 PDF、Word、Excel、PowerPoint、文本等格式</p>
            </div>
          </div>
        </div>

        <!-- 文件列表 -->
        <div v-if="uploadFiles.length > 0" class="file-list">
          <div class="file-list-header">
            <span>已选择文件 ({{ uploadFiles.length }}/{{ maxFileCount }})</span>
            <el-button size="small" text @click="uploadFiles = []">清空列表</el-button>
          </div>

          <div class="file-items">
            <div
              v-for="fileItem in uploadFiles"
              :key="fileItem.id"
              class="file-item"
              :class="{
                uploading: fileItem.status === 'uploading',
                success: fileItem.status === 'success',
                error: fileItem.status === 'error',
                existing: fileItem.isExisting
              }"
            >
              <div class="file-info">
                <!-- <el-icon class="file-icon">
                  <Document />
                </el-icon> -->
                <component :is="getDocumentIcon(fileItem.name)"  />
                <div class="file-details">
                  <div class="file-name">{{ fileItem.name }}</div>
                  <div class="file-size">{{ formatFileSize(fileItem.size) }}</div>
                </div>
              </div>

              <div class="file-status">
                <!-- 准备状态 -->
                <template v-if="fileItem.status === 'ready'">
                  <el-button
                    size="small"
                    text
                    type="danger"
                    @click="removeFile(fileItem.id)"
                    :disabled="isUploading"
                  >
                    <el-icon><Close /></el-icon>
                  </el-button>
                </template>

                <!-- 上传中状态 -->
                <template v-else-if="fileItem.status === 'uploading'">
                  <div class="upload-progress">
                    <el-progress
                      :percentage="fileItem.progress"
                      :stroke-width="4"
                    />
                  </div>
                </template>

                <!-- 成功状态 -->
                <template v-else-if="fileItem.status === 'success'">
                  <div class="success-status">
                    <el-tooltip :content="fileItem.message || (fileItem.isExisting ? '文件已存在' : '上传成功')" placement="top">
                      <el-icon
                        class="status-icon"
                        :class="fileItem.isExisting ? 'existing-icon' : 'success-icon'"
                      >
                        <InfoFilled v-if="fileItem.isExisting" />
                        <Check v-else />
                      </el-icon>
                    </el-tooltip>
                    <span class="status-text" :class="{ existing: fileItem.isExisting }">
                      {{ fileItem.isExisting ? '已存在' : '成功' }}
                    </span>
                  </div>
                </template>

                <!-- 错误状态 -->
                <template v-else-if="fileItem.status === 'error'">
                  <el-tooltip :content="fileItem.error" placement="top">
                    <el-icon class="status-icon error-icon"><Warning /></el-icon>
                  </el-tooltip>
                </template>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <!-- 第一步的按钮 -->
          <template v-if="uploadStep === 1">
            <el-button @click="cancelUpload">取消</el-button>
            <el-button
              type="primary"
              @click="nextStep"
              :disabled="!selectedKnowledgeBase"
            >
              下一步
            </el-button>
          </template>

          <!-- 第二步的按钮 -->
          <template v-if="uploadStep === 2">
            <el-button @click="prevStep" :disabled="isUploading">上一步</el-button>
            <el-button @click="cancelUpload" :disabled="isUploading">取消</el-button>
            <el-button
              type="primary"
              @click="startUpload"
              :loading="isUploading"
              :disabled="uploadFiles.length === 0 || isUploading"
            >
              {{ isUploading ? '上传中...' : '开始上传' }}
            </el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick } from 'vue'
import {
  FolderOpened,
  Plus,
  FolderAdd,
  Refresh,
  Document,
  Clock,
  View,
  Delete,
  Upload,
  Select,
  Close,
  Check,
  Warning,
  InfoFilled,
  ArrowDown,
  DocumentCopy,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getDocumentIcon } from '@/utils/icon'
import { knowledgeList, documentList, createKnowledge, uploadDocument ,deleteCategoryApi,deleteCurrentDocument} from '@/api/knowledge'

// 响应式数据
const tableData = ref([])
const selectedCategory = ref(null)
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)

// 选择相关数据
const selectedDocuments = ref([])
const documentTable = ref(null)

// 分页配置
const pageSizes = ref([10, 20, 50, 100])

// 创建知识库相关数据
const createKnowledgeDialogVisible = ref(false)
const isCreatingKnowledge = ref(false)
const createKnowledgeFormRef = ref(null)

// 新建文档相关数据
const createDocumentDialogVisible = ref(false)
const uploadStep = ref(1) // 1: 选择知识库, 2: 上传文件
const selectedKnowledgeBase = ref(null)
const uploadFiles = ref([])
const isUploading = ref(false)
const uploadProgress = ref({})
const uploadResults = ref([])
const maxFileCount = 30

// 创建知识库表单数据
const createKnowledgeForm = ref({
  name: '',
  description: '',
  public: false
})

// 表单验证规则
const createKnowledgeRules = {
  name: [
    { required: true, message: '请输入知识库名称', trigger: 'blur' },
    { min: 2, max: 50, message: '知识库名称长度应在 2-50 个字符之间', trigger: 'blur' },
    {
      pattern: /^[a-zA-Z0-9\u4e00-\u9fa5_-]+$/,
      message: '知识库名称只能包含中文、英文、数字、下划线和短横线',
      trigger: 'blur'
    }
  ],
  description: [
    { max: 200, message: '描述长度不能超过 200 个字符', trigger: 'blur' }
  ]
}

// 计算属性
const tableMaxHeight = computed(() => {
  // 为分页组件预留空间，动态计算表格最大高度
  return total.value > 0 ? 'calc(100vh - 460px)' : 'calc(100vh - 360px)'
})

// 表格样式配置
const emptyText = computed(() => {
  return selectedCategory.value
    ? `「${selectedCategory.value.name}」分类下暂无文档`
    : '暂无文档数据'
})


// 时间格式化
const formatTime = (timeStr) => {
  if (!timeStr) return '未知时间'

  try {
    const date = new Date(timeStr)
    const now = new Date()
    const diff = now - date

    // 小于1分钟
    if (diff < 60000) {
      return '刚刚'
    }
    // 小于1小时
    if (diff < 3600000) {
      return `${Math.floor(diff / 60000)}分钟前`
    }
    // 小于1天
    if (diff < 86400000) {
      return `${Math.floor(diff / 3600000)}小时前`
    }
    // 小于7天
    if (diff < 604800000) {
      return `${Math.floor(diff / 86400000)}天前`
    }

    // 超过7天显示具体日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    })
  } catch (error) {
    return '时间格式错误'
  }
}

const refreshCategories = async () => {
  try {
    await loadCategories()
    ElMessage.success('分类已刷新')
  } catch (error) {
    // ElMessage.error('刷新分类失败')
  }
}

/**
 * 转换API分类数据为组件需要的格式
 * @param {Array} apiCategories - API返回的分类数据
 * @returns {Array} 转换后的分类数据
 */
const transformCategories = (apiCategories) => {
  if (!Array.isArray(apiCategories)) {
    console.warn('API分类数据格式错误:', apiCategories)
    return []
  }

  return apiCategories.map(category => ({
    id: category.id,
    name: category.name,
    name_md5: category.name_md5,
    description: category.description || '暂无描述',
    doc_count: category.doc_count || 0,
    created_at: category.created_at,
    updated_at: category.updated_at,
    public: category.public,
    // 为树组件添加必要的字段
    label: category.name,
    children: [], // 当前API返回的是扁平结构，如果后续支持层级可以在这里处理
    // 格式化的时间显示
    createdTime: formatTime(category.created_at),
    updatedTime: formatTime(category.updated_at),
    // 分类图标（根据名称推断）
    icon: getCategoryIcon(category.name),
    // 分类类型（根据名称推断）
    type: getCategoryType(category.name)
  }))
}

/**
 * 转换API文档数据为表格需要的格式
 * @param {Array} apiDocuments - API返回的文档数据
 * @returns {Array} 转换后的文档数据
 */
const transformDocuments = (apiDocuments) => {
  if (!Array.isArray(apiDocuments)) {
    console.warn('API文档数据格式错误:', apiDocuments)
    return []
  }

  return apiDocuments.map(doc => ({
    id: doc.id,
    title: doc.original_filename || '未命名文档',
    description: doc.summary || '',
    status: doc.analysis_status || 'not_started',
    author: '', // API中没有作者信息，使用默认值
    updateTime: doc.upload_time,
    // 原始API数据
    file_hash: doc.file_hash,
    bucket_name: doc.bucket_name,
    file_size: doc.file_size,
    file_type: doc.file_type,
    processed_status: doc.processed_status,
    // 格式化的文件大小
    formattedSize: formatFileSize(doc.file_size),
    // 格式化的时间
    formattedTime: formatTime(doc.upload_time)
  }))
}

const getStatusName = (status) => {
  const statusMap = {
    analyzing: '分析中...',
    failed: '分析失败',
    completed: '已完成',
    not_started: '未分析'
  }
  return statusMap[status] || status
}

const getStatusType = (status) => {
  const typeMap = {
    analyzing: 'warning',
    failed: 'danger',
    completed: 'success',
    not_started: 'info'
  }
  return typeMap[status] || 'info'
}

/**
 * 格式化文件大小
 * @param {number} bytes - 文件大小（字节）
 * @returns {string} 格式化后的文件大小
 */
const formatFileSize = (bytes) => {
  if (!bytes || bytes === 0) return '0 B'

  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

/**
 * 根据分类名称推断图标
 * @param {string} categoryName - 分类名称
 * @returns {string} 图标组件名
 */
const getCategoryIcon = (categoryName) => {
  const name = categoryName.toLowerCase()

  if (name.includes('技术') || name.includes('开发')) return 'Monitor'
  if (name.includes('api') || name.includes('接口')) return 'Connection'
  if (name.includes('用户') || name.includes('操作')) return 'Guide'
  if (name.includes('数据') || name.includes('分析')) return 'DataAnalysis'
  if (name.includes('地理') || name.includes('位置') || name.includes('三江源')) return 'MapLocation'

  return 'Box' // 默认图标
}

/**
 * 根据分类名称推断类型
 * @param {string} categoryName - 分类名称
 * @returns {string} 分类类型
 */
const getCategoryType = (categoryName) => {
  const name = categoryName.toLowerCase()

  if (name.includes('技术') || name.includes('开发')) return 'technical'
  if (name.includes('api') || name.includes('接口')) return 'api'
  if (name.includes('用户') || name.includes('操作')) return 'user'
  if (name.includes('数据') || name.includes('分析')) return 'data'

  return 'general' // 默认类型
}

/**
 * 选择分类
 * @param {Object|null} category - 选中的分类对象，null表示选择全部
 */
const selectCategory = async (category) => {
  selectedCategory.value = category
  // console.log('选择分类:', category)

  // 重置分页到第一页
  currentPage.value = 1

  // 清除选择状态
  clearSelection()

  // 重新加载文档列表
  await loadDocuments(category, 1, pageSize.value)
}

/**
 * 分页大小改变事件
 * @param {number} newSize - 新的每页显示数量
 */
const handleSizeChange = async (newSize) => {
  // console.log('分页大小改变:', newSize)
  pageSize.value = newSize
  currentPage.value = 1 // 重置到第一页
  await loadDocuments(selectedCategory.value, 1, newSize)
}

/**
 * 当前页改变事件
 * @param {number} newPage - 新的页码
 */
const handleCurrentChange = async (newPage) => {
  // console.log('页码改变:', newPage)
  currentPage.value = newPage
  await loadDocuments(selectedCategory.value, newPage, pageSize.value)
}
const isLoadingCategories = ref(false)
const categories = ref([])
/**
 * 获取知识分类列表
 */
const categoryTotal = ref()
const loadCategories = async () => {
  isLoadingCategories.value = true

  try {
    // console.log('开始获取知识分类列表...')
    const response = await knowledgeList()
    // console.log('API响应:', response)

    if (response && response.code === 0 && Array.isArray(response.data)) {
      const transformedCategories = transformCategories(response.data)
      categories.value = transformedCategories
      categoryTotal.value = categories.value.reduce((sum,item)=>sum + item.doc_count,0) 
      
      // console.log('转换后的分类数据:', transformedCategories)
      // ElMessage.success(`成功加载 ${transformedCategories.length} 个知识分类`)

      // 如果没有选中的分类，默认选中第一个
      if (!selectedCategory.value && transformedCategories.length > 0) {
        selectedCategory.value = transformedCategories[0]
        // console.log('默认选中分类:', selectedCategory.value)
      }
    } else {
      console.error('API响应格式错误:', response)
      // ElMessage.error('获取知识分类失败：数据格式错误')
      categories.value = []
    }
  } catch (error) {
    console.error('获取知识分类失败:', error)
    // ElMessage.error(`获取知识分类失败: ${error.message || '未知错误'}`)
    categories.value = []
  } finally {
    isLoadingCategories.value = false
  }
}
const isLoadingDocuments = ref(false)
/**
 * 获取知识库中的文档列表
 * @param {Object} category - 选中的分类对象
 * @param {number} page - 页码
 * @param {number} size - 每页数量
 */
const loadDocuments = async (category = null, page = 1, size = 20) => {
  isLoadingDocuments.value = true

  try {
    const params = {
      page: page,
      page_size: size
    }

    // 如果有选中的分类，添加分类参数
    if (category && category.name_md5) {
      params.collection_hash = category.name_md5
      // console.log(`获取分类「${category.name}」的文档列表，collection_hash: ${category.name_md5}`)
    } else {
      // console.log('获取所有文档列表')
    }

    // console.log('文档列表请求参数:', params)
    const response = await documentList(params)
    // console.log('文档列表API响应:', response)

    if (response && response.code === 0) {
      // 根据实际API响应结构处理数据
      const documents = response.documents || response.data || []
      const totalCount = response.total || documents.length

      // 转换文档数据格式
      tableData.value = transformDocuments(documents)
      total.value = totalCount
      currentPage.value = page

      // 清除选择状态
      clearSelection()

      // console.log('转换后的文档数据:', tableData.value)

      const categoryName = category ? category.name : '全部分类'
      // ElMessage.success(`「${categoryName}」加载了 ${documents.length} 个文档`)
    } else {
      console.error('文档列表API响应格式错误:', response)
      // ElMessage.error('获取文档列表失败：数据格式错误')
      tableData.value = []
      total.value = 0
    }
  } catch (error) {
    console.error('获取文档列表失败:', error)
    // ElMessage.error(`获取文档列表失败: ${error.message || '未知错误'}`)
    tableData.value = []
    total.value = 0
  } finally {
    isLoadingDocuments.value = false
  }
}

// 操作方法
const createDocument = () => {
  // 重置对话框状态
  uploadStep.value = 1
  selectedKnowledgeBase.value = null
  uploadFiles.value = []
  uploadProgress.value = {}
  uploadResults.value = []
  isUploading.value = false

  // 打开对话框
  createDocumentDialogVisible.value = true
    // 刷新知识库分类列表
}

/**
 * 打开创建知识库弹窗
 */
const createCategory = () => {
  createKnowledgeDialogVisible.value = true
}

/**
 * 重置创建知识库表单
 */
const resetCreateKnowledgeForm = () => {
  // 延迟重置，避免在弹窗关闭动画期间执行
  if (createKnowledgeFormRef.value) {
    createKnowledgeFormRef.value.resetFields()
  }
  createKnowledgeForm.value = {
    name: '',
    description: '',
    public: false
  }
}

/**
 * 取消创建知识库
 */
const cancelCreateKnowledge = () => {
  createKnowledgeDialogVisible.value = false
  // resetCreateKnowledgeForm()
}

/**
 * 确认创建知识库
 */
const confirmCreateKnowledge = async () => {
  if (!createKnowledgeFormRef.value) return

  try {
    // 表单验证
    await createKnowledgeFormRef.value.validate()

    isCreatingKnowledge.value = true

    // 准备API请求数据
    const requestData = {
      name: createKnowledgeForm.value.name.trim(),
      description: createKnowledgeForm.value.description.trim() || '',
      public: createKnowledgeForm.value.public
    }

    // console.log('创建知识库请求数据:', requestData)

    // 调用创建知识库API
    const response = await createKnowledge(requestData)
    // console.log('创建知识库API响应:', response)

    if (response && response.code === 0) {
      ElMessage.success(`知识库「${requestData.name}」创建成功！`)

      // 关闭弹窗
      createKnowledgeDialogVisible.value = false

      // 重置表单
      // resetCreateKnowledgeForm()

      // 刷新知识库分类列表
      await loadCategories()

      // 如果创建成功后返回了新知识库的信息，可以自动选中
      if (response.data && response.data.id) {
        const newCategory = categories.value.find(cat => cat.id === response.data.id)
        if (newCategory) {
          selectedCategory.value = newCategory
          await loadDocuments(newCategory, 1, pageSize.value)
        }
      }
    } else {
      const errorMessage = response?.message || '创建知识库失败'
      console.error('创建知识库失败:', response)
      ElMessage.error(errorMessage)
    }
  } catch (error) {
    if (error.name === 'ValidationError') {
      // 表单验证失败
      ElMessage.warning('请检查表单填写是否正确')
    } else {
      console.error('创建知识库失败:', error)
      const errorMessage = error.message || '创建知识库失败，请重试'
      ElMessage.error(errorMessage)
    }
  } finally {
    isCreatingKnowledge.value = false
  }
}

const deleteCategory = async (category) => {

  try {
    await ElMessageBox.confirm(
      `"${category.name}"知识库中现有${category.doc_count}文档，是否确认删除`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '删除中...'

            try {
              // 调用删除API
              const params = {
                knowledge_hash: category.name_md5,
              }

              await deleteCategoryApi(params)

              ElMessage.success('分类删除成功')
              await loadCategories()

              // 如果删除的是当前选中的分类，重置选择
              if (selectedCategory.value?.id === category.id) {
                selectCategory(null)
              }

              done()
            } catch (error) {
              console.error('删除分类失败:', error)
              ElMessage.error('删除分类失败')
              instance.confirmButtonLoading = false
              instance.confirmButtonText = '确定删除'
            }
          } else {
            done()
          }
        }
      }
    )
  } catch (error) {
    // 用户取消操作，不显示错误信息
    if (error !== 'cancel') {
      console.error('删除分类操作异常:', error)
    }
  }
}


// 选择相关方法
const handleSelectionChange = (selection) => {
  selectedDocuments.value = selection
}

const handleRowClick = (row, column) => {
  // 如果点击的是选择列，不执行查看操作
  if (column && column.type === 'selection') {
    return
  }
  viewDocument(row)
}

const clearSelection = () => {
  if (documentTable.value) {
    documentTable.value.clearSelection()
  }
  selectedDocuments.value = []
}

// 批量操作方法
const handleBatchAction = async (command) => {
  if (selectedDocuments.value.length === 0) {
    ElMessage.warning('请先选择要操作的文档')
    return
  }

  switch (command) {
    case 'parse':
      await batchParseDocuments()
      break
    case 'delete':
      await batchDeleteDocuments()
      break
    case 'cancel':
      clearSelection()
      break
  }
}

const batchParseDocuments = async () => {
  console.log(selectedDocuments.value);
  
  try {
    await ElMessageBox.confirm(
      `确定要解析选中的 ${selectedDocuments.value.length} 个文档吗？`,
      '批量解析确认',
      {
        confirmButtonText: '确定解析',
        cancelButtonText: '取消',
        type: 'info',
      }
    )

    ElMessage.success(`开始解析 ${selectedDocuments.value.length} 个文档`)
    // 这里可以调用批量解析API
    clearSelection()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量解析操作异常:', error)
    }
  }
}

const batchDeleteDocuments = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${selectedDocuments.value.length} 个文档吗？此操作不可恢复。`,
      '批量删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '删除中...'

            try {
              // 批量删除文档
              // 调用api方法
              ElMessage.success(`成功删除 ${selectedDocuments.value.length} 个文档`)

              // 刷新数据
              await loadDocuments(selectedCategory.value, currentPage.value, pageSize.value)
              await loadCategories()
              clearSelection()
              done()
            } catch (error) {
              console.error('批量删除失败:', error)
              ElMessage.error('批量删除失败，请重试')
              instance.confirmButtonLoading = false
              instance.confirmButtonText = '确定删除'
            }
          } else {
            done()
          }
        }
      }
    )
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除操作异常:', error)
    }
  }
}

const viewDocument = (doc) => {
  // console.log('查看文档:', doc)
  ElMessage.info(`查看文档: ${doc.title}`)
}

const deleteDocument = async (doc) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除文档「${doc.title}」吗？此操作不可恢复。`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning',
        confirmButtonClass: 'el-button--danger',
        beforeClose: async (action, instance, done) => {
          if (action === 'confirm') {
            instance.confirmButtonLoading = true
            instance.confirmButtonText = '删除中...'

            try {
              const params = {
                file_hash: doc.file_hash,
                knowledge_hash: doc.bucket_name
              }
              await deleteCurrentDocument(params)
              ElMessage.success(`文档「${doc.title}」已删除`)

              // 刷新表格数据
              await loadDocuments(selectedCategory.value, currentPage.value, pageSize.value)
              // 刷新知识库分类列表
              await loadCategories()
              done()
            } catch (error) {
              console.error('删除文档失败:', error)
              ElMessage.error('删除失败，请重试')
              instance.confirmButtonLoading = false
              instance.confirmButtonText = '确定删除'
            }
          } else {
            done()
          }
        }
      }
    )
  } catch (error) {
    // 用户取消操作，不显示错误信息
    if (error !== 'cancel') {
      console.error('删除文档操作异常:', error)
    }
  }
}

// 新建文档相关方法
/**
 * 选择知识库
 */
const selectKnowledgeBase = (knowledgeBase) => {
  selectedKnowledgeBase.value = knowledgeBase
}

/**
 * 进入下一步（文件上传）
 */
const nextStep = () => {
  if (!selectedKnowledgeBase.value) {
    ElMessage.warning('请先选择目标知识库')
    return
  }
  uploadStep.value = 2
}

/**
 * 返回上一步（知识库选择）
 */
const prevStep = () => {
  uploadStep.value = 1
}

/**
 * 处理文件选择
 */
const handleFileSelect = (event) => {
  console.log(event);
  
  const files = Array.from(event.target.files)

  if (files.length === 0) return

  // 检查文件数量限制
  if (uploadFiles.value.length + files.length > maxFileCount) {
    ElMessage.warning(`最多只能上传 ${maxFileCount} 个文件`)
    return
  }

  // 添加文件到上传列表
  files.forEach(file => {
    const fileId = Date.now() + Math.random()
    uploadFiles.value.push({
      id: fileId,
      file: file,
      name: file.name,
      size: file.size,
      status: 'ready', // ready, uploading, success, error, exists
      progress: 0,
      error: null,
      message: null, // 用于存储特殊状态信息
      isExisting: false // 标识文件是否已存在
    })
  })

  // 清空input
  event.target.value = ''
}

/**
 * 移除文件
 */
const removeFile = (fileId) => {
  const index = uploadFiles.value.findIndex(f => f.id === fileId)
  if (index > -1) {
    uploadFiles.value.splice(index, 1)
  }
}

/**
 * 开始上传所有文件
 */
const startUpload = async () => {
  if (uploadFiles.value.length === 0) {
    ElMessage.warning('请先选择要上传的文件')
    return
  }

  if (!selectedKnowledgeBase.value) {
    ElMessage.error('请先选择目标知识库')
    return
  }

  isUploading.value = true
  uploadResults.value = []

  try {
    // 并发上传文件（限制并发数为3）
    const concurrencyLimit = 3
    const uploadPromises = []

    for (let i = 0; i < uploadFiles.value.length; i += concurrencyLimit) {
      const batch = uploadFiles.value.slice(i, i + concurrencyLimit)
      const batchPromises = batch.map(fileItem => uploadSingleFile(fileItem))
      uploadPromises.push(...batchPromises)

      // 等待当前批次完成再开始下一批次
      if (i + concurrencyLimit < uploadFiles.value.length) {
        await Promise.allSettled(batchPromises)
      }
    }

    // 等待所有上传完成
    const results = await Promise.allSettled(uploadPromises)

    // 统计上传结果
    const successCount = results.filter(r => r.status === 'fulfilled').length
    const failCount = results.filter(r => r.status === 'rejected').length

    // 统计新上传和已存在的文件
    const newUploadCount = uploadFiles.value.filter(f => f.status === 'success' && !f.isExisting).length
    const existingCount = uploadFiles.value.filter(f => f.status === 'success' && f.isExisting).length

    if (successCount > 0) {
      let message = ''
      if (newUploadCount > 0 && existingCount > 0) {
        message = `成功处理 ${successCount} 个文件：${newUploadCount} 个新上传，${existingCount} 个文件已存在`
      } else if (newUploadCount > 0) {
        message = `成功上传 ${newUploadCount} 个文件`
      } else if (existingCount > 0) {
        message = `${existingCount} 个文件已存在于知识库中`
      }

      if (failCount > 0) {
        message += `，${failCount} 个文件处理失败`
      }

      ElMessage.success(message)

      // 刷新文档列表
      await loadDocuments(selectedCategory.value, currentPage.value, pageSize.value)

      // 不自动关闭对话框，让用户可以继续上传或查看结果
    } else {
      ElMessage.error('所有文件处理失败，请检查网络连接或文件格式')
    }
  } catch (error) {
    console.error('批量上传失败:', error)
    ElMessage.error('上传过程中发生错误，请重试')
  } finally {
    isUploading.value = false
  }
}

/**
 * 上传单个文件
 */
const uploadSingleFile = async (fileItem) => {
  try {
    // 更新文件状态
    fileItem.status = 'uploading'
    fileItem.progress = 0
    fileItem.error = null
    fileItem.message = null
    fileItem.isExisting = false

    // 创建FormData
    const formData = new FormData()
    formData.append('file', fileItem.file)
    formData.append('knowledge_hash', selectedKnowledgeBase.value.name_md5)

    // 上传文件
    const response = await uploadDocument(formData, {
      onProgress: (progressEvent) => {
        if (progressEvent.lengthComputable) {
          fileItem.progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        }
      }
    })

    // 检查响应结果
    if (response && response.code === 0) {
      fileItem.status = 'success'
      fileItem.progress = 100

      // 检查是否为文件已存在的情况
      if (response.message === '文件已存在') {
        fileItem.isExisting = true
        fileItem.message = '文件已存在'

        uploadResults.value.push({
          file: fileItem.name,
          status: 'success',
          message: '文件已存在于知识库中',
          isExisting: true,
          fileInfo: response.file_info,
          uploadTime: response.upload_time
        })
      } else {
        fileItem.message = '上传成功'

        uploadResults.value.push({
          file: fileItem.name,
          status: 'success',
          message: '上传成功',
          isExisting: false
        })
         // 刷新分类列表以更新文档计数
        await loadCategories()
         // 刷新文档列表
        await loadDocuments(selectedCategory.value, currentPage.value, pageSize.value)
      }
    } else {
      // API返回非成功状态
      throw new Error(response?.message || '上传失败')
    }

    return response
  } catch (error) {
    // 上传失败
    fileItem.status = 'error'
    fileItem.error = error.message || '上传失败'
    fileItem.message = error.message || '上传失败'

    uploadResults.value.push({
      file: fileItem.name,
      status: 'error',
      message: error.message || '上传失败',
      isExisting: false
    })

    throw error
  }
}

/**
 * 取消上传
 */
const cancelUpload = () => {
  createDocumentDialogVisible.value = false
}



onMounted(async () => {
  // console.log('知识管理页面已加载')

  try {
    // 首先加载分类列表
    await loadCategories()

    // 然后加载文档列表（如果有选中的分类，会自动加载该分类的文档）
    await loadDocuments(selectedCategory.value, 1, pageSize.value)
  } catch (error) {
    console.error('页面初始化失败:', error)
    ElMessage.error('页面初始化失败，请刷新重试')
  }
})
</script>
<style lang="scss">
// 知识库选项样式 - 主题适配
.el-select-dropdown {
  border-radius: 12px;
  box-shadow: var(--box-shadow-lg);
  border: 1px solid var(--border-color-lighter);
  padding: 8px;
  background: var(--bg-color) !important; // 主题背景适配

  .el-select-dropdown__item {
    border-radius: 8px;
    margin-bottom: 4px;
    padding-left: 6px;
    transition: all 0.2s ease;
    color: var(--text-color-primary) !important; // 主题文字色彩
    background: transparent;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: var(--primary-lighter) !important;
      color: var(--primary-color) !important;
    }

    &.is-selected {
      background: var(--primary-color) !important;
      color: white !important;

      .kb-option-content {
        .kb-option-main {
          .kb-icon {
            color: white !important;
          }

          .kb-count {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border-color: transparent !important;
          }
        }

        .kb-description {
          color: rgba(255, 255, 255, 0.8) !important;
        }
      }

      // 集成式选项样式
      .kb-option-integrated {
        .kb-icon {
          color: white !important;
        }

        .kb-name {
          color: white !important;
        }

        .kb-count {
          background: rgba(255, 255, 255, 0.2) !important;
          color: white !important;
          border-color: transparent !important;
        }
      }
    }
  }
}

// 批量操作下拉菜单样式
.el-dropdown-menu {
  border-radius: 8px;
  box-shadow: var(--box-shadow-lg);
  border: 1px solid var(--border-color-lighter);
  padding: 4px;
  background: var(--bg-color) !important;

  .el-dropdown-menu__item {
    border-radius: 6px;
    margin-bottom: 2px;
    padding: 8px 12px;
    transition: all 0.2s ease;
    color: var(--text-color-primary) !important;
    background: transparent;
    display: flex;
    align-items: center;
    gap: 8px;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: var(--fill-color-light) !important;
      color: var(--primary-color) !important;
    }

    &.is-divided {
      border-top: 1px solid var(--border-color-lighter);
      margin-top: 4px;
      padding-top: 8px;
    }

    .el-icon {
      font-size: 14px;
    }
  }
}

</style>
<style lang="scss" scoped>
.knowledge-management {
  max-width: 1600px;
  margin: 0 auto;
  //padding: 12px;
  background-color: var(--bg-color-page);
  min-height: calc(100vh - 11vh);
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: var(--card-bg);
  border-radius: 16px;
  border: 1px solid var(--border-color-lighter);
  box-shadow: var(--box-shadow-sm);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    box-shadow: var(--box-shadow);
    transform: translateY(-1px);
  }

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    .header-left {
      .page-title {
        display: flex;
        align-items: center;
        gap: 12px;
        margin: 0 0 8px 0;
        font-size: 28px;
        font-weight: 700;
        color: var(--text-color-primary);
        transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        .el-icon {
          font-size: 32px;
          color: var(--primary-color);
          transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
      }

      .page-description {
        margin: 0;
        color: var(--text-color-secondary);
        font-size: 16px;
        transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
    }

    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
}

.main-content {
  .category-panel,
  .document-panel {
    height: calc(100vh - 280px);
    background: var(--card-bg);
    border: 1px solid var(--border-color-lighter);
    border-radius: 16px;
    box-shadow: var(--box-shadow-sm);
    transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &:hover {
      box-shadow: var(--box-shadow);
      transform: translateY(-1px);
    }

    :deep(.el-card__header) {
      background: var(--fill-color-extra-light);
      border-bottom: 1px solid var(--border-color-lighter);
      border-radius: 16px 16px 0 0;
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    :deep(.el-card__body) {
      background: var(--card-bg);
      transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      color: var(--text-color-primary);
      font-weight: 600;
      font-size: 16px;
      transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      .header-actions {
        display: flex;
        gap: 8px;
        align-items: center;
      }
    }
  }

  .category-panel {
    overflow: auto;
    .category-list {
      height: calc(100% - 80px);
      padding: 16px;
      overflow-y: auto;

      .category-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 12px 16px;
        margin-bottom: 8px;
        border-radius: 12px;
        background: var(--card-bg);
        border: 1px solid var(--border-color-lighter);
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &:hover {
          background: var(--fill-color-light);
          border-color: var(--primary-color-light-7);
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.active {
          background: var(--primary-color-light-9);
          border-color: var(--primary-color);
          color: var(--primary-color);

          .category-icon {
            color: var(--primary-color);
          }

          .category-name {
            color: var(--primary-color);
            font-weight: 600;
          }
        }

        &.all-category {
          cursor:default;
          margin-bottom: 16px;
          border: 2px dashed var(--border-color-light);

          &:hover {
            border-color: var(--primary-color-light-5);
          }

          &.active {
            border-color: var(--primary-color);
            background: var(--primary-color-light-9);
          }
        }

        .category-info {
          display: flex;
          align-items: center;
          gap: 12px;
          flex: 1;
          min-width: 0;

          .category-icon {
            font-size: 18px;
            color: var(--text-color-secondary);
            transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          }

          .category-details {
            display: flex;
            flex-direction: column;
            gap: 4px;
            flex: 1;
            min-width: 0;

            .category-name {
              font-size: 14px;
              font-weight: 500;
              color: var(--text-color-primary);
              transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }

            .category-desc {
              font-size: 12px;
              color: var(--text-color-placeholder);
              transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
              white-space: nowrap;
              overflow: hidden;
              text-overflow: ellipsis;
            }
          }

          .category-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color-primary);
            transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          }
        }

 
      }

      .empty-categories {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        height: 200px;
        color: var(--text-color-placeholder);
      }
    }
  }

  .document-panel {
    .document-list {
      height: calc(100% - 80px);
      padding: 16px;
      display: flex;
      flex-direction: column;
      overflow: hidden;

      // 批量操作工具栏样式
      .batch-actions-toolbar {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 12px 16px;
        margin-bottom: 16px;
        background: var(--primary-color-light-9);
        border: 1px solid var(--primary-color-light-7);
        border-radius: 8px;
        transition: all 0.3s ease;

        .selected-info {
          display: flex;
          align-items: center;
          gap: 8px;
          color: var(--primary-color);
          font-weight: 500;
          font-size: 14px;

          .el-icon {
            font-size: 16px;
          }
        }

        .batch-actions {
          display: flex;
          gap: 8px;
        }
      }

      .list-view {
        flex: 1;
        border-radius: 12px;
        overflow: hidden;
        border: 1px solid var(--border-color-lighter);
        min-height: 0; // 确保flex子元素可以收缩

        :deep(.el-table) {
          background: var(--card-bg) !important;
          border-radius: 12px;
          overflow: hidden;
          border: 1px solid var(--border-color-lighter);

          // 表头样式
          .el-table__header-wrapper {
            background: var(--fill-color-extra-light) !important;
          }

          .el-table__header {
            background: var(--fill-color-extra-light) !important;

            th {
              background: var(--fill-color-extra-light) !important;
              color: var(--text-color-primary) !important;
              font-weight: 600;
              border-bottom: 1px solid var(--border-color-lighter) !important;
              border-right: 1px solid var(--border-color-lighter);
              transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
              padding: 16px 12px;

              &:last-child {
                border-right: none;
              }

              .cell {
                color: var(--text-color-primary) !important;
                font-weight: 600;
              }
            }
          }

          // 表体样式
          .el-table__body-wrapper {
            background: var(--card-bg) !important;
          }

          .el-table__body {
            background: var(--card-bg) !important;

            tr {
              background: var(--card-bg) !important;
              transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              &:hover {
                background: var(--fill-color-light) !important;
                transform: scale(1.002);
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
              }

              &.current-row {
                background: var(--primary-color-light-9) !important;

                td {
                  background: var(--primary-color-light-9) !important;
                }
              }

              // 条纹行样式
              &.el-table__row--striped {
                background: var(--fill-color-lighter) !important;

                td {
                  background: var(--fill-color-lighter) !important;
                }

                &:hover {
                  background: var(--fill-color-light) !important;

                  td {
                    background: var(--fill-color-light) !important;
                  }
                }
              }

              td {
                background: var(--card-bg) !important;
                border-bottom: 1px solid var(--border-color-lighter) !important;
                border-right: 1px solid var(--border-color-lighter);
                color: var(--text-color-regular) !important;
                transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
                padding: 16px 12px;

                &:last-child {
                  border-right: none;
                }

                .cell {
                  color: var(--text-color-regular) !important;
                }
              }
            }
          }

          // 空状态样式
          .el-table__empty-block {
            background: var(--card-bg) !important;
            color: var(--text-color-placeholder);
            padding: 60px 20px;

            .el-table__empty-text {
              color: var(--text-color-placeholder) !important;
              font-size: 14px;
            }
          }

          // 固定列样式
          .el-table__fixed,
          .el-table__fixed-right {
            background: var(--card-bg) !important;

            .el-table__fixed-header-wrapper,
            .el-table__fixed-body-wrapper {
              background: var(--card-bg) !important;
            }
          }
        }

        .document-row {
          cursor: pointer;

          &:hover {
            background-color: var(--fill-color-light);
          }

          .doc-icon {
            color: var(--primary-color);
            font-size: 18px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          }

          &:hover .doc-icon {
            transform: scale(1.2);
            color: var(--primary-color-dark);
          }
        }
      }

      // 分页组件样式

      .pagination-wrapper {
        margin-top: 16px;
        padding: 16px 0;
        border-top: 1px solid var(--border-color-lighter);
        background: var(--card-bg);
        border-radius: 0 0 12px 12px;

        .document-pagination {
          display: flex;
          justify-content: center;
          align-items: center;


        }
      }
      :deep(.el-pagination) {
        .el-pagination__total,
        .el-pagination__jump {
          color: var(--text-color-regular);
          .el-input {
            .el-input__wrapper {
              background: var(--fill-color-light);
              border: 1px solid var(--border-color-light);
              transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              &:hover {
                border-color: var(--primary-color);
              }

              &.is-focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px var(--primary-color-light-9);
              }
            }
          }
        }

        .el-pagination__sizes {
          .el-select {
            .el-select__wrapper {
              background-color: var(--fill-color-light);
              border: 1px solid var(--border-color-light);
              transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              &:hover {
                border-color: var(--primary-color);
              }

              &.is-focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px var(--primary-color-light-9);
              }
            }

          }
        }

        .btn-prev,
        .btn-next {
          background: var(--card-bg);
          border: 1px solid var(--border-color-lighter);
          color: var(--text-color-regular);
          transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

          &:hover:not(:disabled) {
            background: var(--primary-color-light-9);
            border-color: var(--primary-color);
            color: var(--primary-color);
          }

          &:disabled {
            background: var(--fill-color-lighter);
            border-color: var(--border-color-lighter);
            color: var(--text-color-disabled);
          }
        }

        .el-pager {
          li {
            background: var(--card-bg);
            border: 1px solid var(--border-color-lighter);
            color: var(--text-color-regular);
            margin: 0 2px;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

            &:hover {
              background: var(--primary-color-light-9);
              border-color: var(--primary-color);
              color: var(--primary-color);
            }

            &.is-active {
              background: var(--primary-color);
              border-color: var(--primary-color);
              color: white;
            }
          }
        }
      }
    }

    // 表格内部元素样式
    .doc-icon {
      color: var(--primary-color);
      font-size: 20px;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .title-cell {
      .title-text {
        display: block;
        font-weight: 600;
        color: var(--text-color-primary);
        margin-bottom: 4px;
        transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .title-desc {
        display: block;
        font-size: 12px;
        color: var(--text-color-placeholder);
        line-height: 1.4;
        // transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        // 单行省略号效果
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        max-width: 100%;
      }
    }

    .author-cell {
      display: flex;
      align-items: center;
      gap: 8px;

      .author-avatar {
        background: var(--primary-color-light-8);
        color: var(--primary-color);
        font-size: 12px;
        font-weight: 600;
        transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .author-name {
        font-size: 13px;
        color: var(--text-color-regular);
        transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
    }

    .time-cell {
      display: flex;
      align-items: center;
      gap: 6px;
      font-size: 13px;
      color: var(--text-color-secondary);

      .time-icon {
        font-size: 14px;
        color: var(--text-color-placeholder);
        transition: color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }
    }

    .action-buttons {
      display: flex;
      gap: 8px;
      justify-content: center;

      .el-button {
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &:hover {
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .knowledge-management {
    padding: 16px;
  }

  .main-content {
    :deep(.el-row) {
      flex-direction: column;

      .el-col {
        width: 100% !important;
        max-width: 100% !important;
        margin-bottom: 16px;

        &:last-child {
          margin-bottom: 0;
        }
      }
    }

    .category-panel {
      height: 300px;
    }

    .document-panel {
      height: calc(100vh - 400px);

      .document-list {
        .pagination-wrapper {
          .document-pagination {
            :deep(.el-pagination) {
              flex-wrap: wrap;
              gap: 8px;
              justify-content: center;

              .el-pagination__total,
              .el-pagination__jump {
                order: 3;
                margin-top: 8px;
              }

              .el-pagination__sizes {
                order: 2;
              }

              .el-pager,
              .btn-prev,
              .btn-next {
                order: 1;
              }
            }
          }
        }
      }
    }
  }
}

@media (max-width: 768px) {
  .knowledge-management {
    padding: 12px;
  }

  .page-header {
    padding: 16px;
    margin-bottom: 16px;

    .header-content {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .page-title {
        font-size: 24px;

        .el-icon {
          font-size: 28px;
        }
      }

      .page-description {
        font-size: 14px;
      }
    }
  }

  .main-content {
    .category-panel {
      height: 250px;
    }

    .document-panel {
      height: calc(100vh - 350px);

      .document-list {
        padding: 12px;

        .pagination-wrapper {
          margin-top: 12px;
          padding: 12px 0;

          .document-pagination {
            :deep(.el-pagination) {
              .el-pagination__total,
              .el-pagination__jump,
              .el-pagination__sizes {
                display: none; // 在移动端隐藏这些元素以节省空间
              }

              .el-pager {
                li {
                  margin: 0 1px;
                  min-width: 32px;
                  height: 32px;
                  line-height: 30px;
                  font-size: 12px;
                }
              }

              .btn-prev,
              .btn-next {
                min-width: 32px;
                height: 32px;
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

//// 深色主题特殊处理
//:deep(.dark) {
//  .knowledge-management {
//    background-color: var(--bg-color-page);
//  }
//
//  .page-header {
//    background: var(--card-bg);
//    border-color: var(--border-color-darker);
//  }
//
//  .main-content {
//    .category-panel,
//    .document-panel {
//      background: var(--card-bg);
//      border-color: var(--border-color-darker);
//
//      .el-card__header {
//        background: var(--fill-color-extra-light);
//        border-color: var(--border-color-darker);
//      }
//
//      .el-card__body {
//        background: var(--card-bg);
//      }
//    }
//  }
//
//  // 深色主题下的表格特殊处理
//  .el-table {
//    background: var(--card-bg) !important;
//    border-color: var(--border-color-darker) !important;
//
//    .el-table__header-wrapper {
//      background: var(--fill-color-extra-light) !important;
//    }
//
//    .el-table__header th {
//      background: var(--fill-color-extra-light) !important;
//      border-color: var(--border-color-darker) !important;
//      color: var(--text-color-primary) !important;
//    }
//
//    .el-table__body-wrapper {
//      background: var(--card-bg) !important;
//    }
//
//    .el-table__body tr {
//      background: var(--card-bg) !important;
//
//      &:hover {
//        background: var(--fill-color-light) !important;
//      }
//
//      &.current-row {
//        background: var(--primary-color-light-9) !important;
//      }
//
//      &.el-table__row--striped {
//        background: var(--fill-color-lighter) !important;
//
//        &:hover {
//          background: var(--fill-color-light) !important;
//        }
//      }
//
//      td {
//        background: inherit !important;
//        border-color: var(--border-color-darker) !important;
//        color: var(--text-color-regular) !important;
//      }
//    }
//
//    .el-table__empty-block {
//      background: var(--card-bg) !important;
//      color: var(--text-color-placeholder) !important;
//    }
//  }
//}

// 滚动条主题适配
:deep(.el-scrollbar) {
  .el-scrollbar__bar {
    .el-scrollbar__thumb {
      background: var(--border-color);
      transition: background 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        background: var(--border-color-dark);
      }
    }
  }

  .el-scrollbar__track {
    background: var(--fill-color-lighter);
  }
}

// Element Plus 组件主题适配
:deep(.el-button) {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
}

:deep(.el-tag) {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    transform: scale(1.05);
  }
}

:deep(.el-card) {
  background: var(--card-bg);
  border-color: var(--border-color-lighter);
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  .el-card__header {
    background: var(--fill-color-extra-light);
    border-color: var(--border-color-lighter);
    color: var(--text-color-primary);
  }

  .el-card__body {
    background: var(--card-bg);
    color: var(--text-color-regular);
  }
}

// 全局表格主题覆盖
:deep(.el-table) {
  --el-table-bg-color: var(--card-bg);
  --el-table-tr-bg-color: var(--card-bg);
  --el-table-header-bg-color: var(--fill-color-extra-light);
  --el-table-row-hover-bg-color: var(--fill-color-light);
  --el-table-current-row-bg-color: var(--primary-color-light-9);
  --el-table-header-text-color: var(--text-color-primary);
  --el-table-text-color: var(--text-color-regular);
  --el-table-border-color: var(--border-color-lighter);
  --el-table-border: 1px solid var(--border-color-lighter);

  // 强制应用主题变量
  * {
    border-color: var(--border-color-lighter) !important;
  }

  .el-table__cell {
    border-color: var(--border-color-lighter) !important;
  }
}

// 确保表格在主题切换时正确更新
.knowledge-management {
  .el-table {
    color-scheme: light dark;
  }
}

// 创建知识库弹窗样式
:deep(.el-dialog) {
  background: var(--card-bg);
  .el-dialog__header {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color-lighter);
    padding: 20px 24px 16px;

    .el-dialog__title {
      color: var(--text-color-primary);
      font-size: 18px;
      font-weight: 600;
    }

    .el-dialog__headerbtn {
      .el-dialog__close {
        color: var(--text-color-secondary);
        font-size: 18px;

        &:hover {
          color: var(--text-color-primary);
        }
      }
    }
  }

  .el-dialog__body {
    background: var(--card-bg);
    padding: 24px;

    .el-form {
      .el-form-item {
        margin-bottom: 24px;

        .el-form-item__label {
          color: var(--text-color-primary);
          font-weight: 500;
        }

        .el-form-item__content {
          .el-input {
            .el-input__wrapper {
              background: var(--card-bg);
              border: 1px solid var(--border-color-lighter);
              &:hover {
                border-color: var(--primary-color);
              }

              &.is-focus {
              }

              .el-input__inner {
                color: var(--text-color-regular);
                background: transparent;

                &::placeholder {
                  color: var(--text-color-placeholder);
                }
              }
              .el-input__count .el-input__count-inner {
                background: var(--card-bg);
              }
            }
          }

          .el-textarea {
            .el-input__count {
              background: var(--card-bg);
            }
            .el-textarea__inner {
              background: var(--card-bg);
              border: 1px solid var(--border-color-lighter);
              color: var(--text-color-regular);
              //transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

              &:hover {
                border-color: var(--primary-color);
              }

              &:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px var(--primary-color-light-9);
              }

              &::placeholder {
                color: var(--text-color-placeholder);
              }
            }
          }

          .el-switch {
            .el-switch__core {
              //background: var(--fill-color-lighter);
              border-color: var(--border-color-lighter);

              &:after {
                background: white;
              }
            }

            &.is-checked {
              .el-switch__core {
                background: var(--primary-color);
                border-color: var(--primary-color);
              }
            }
            .el-switch__label{
              color: var(--text-color-primary);
              &.is-active {
                color: var(--primary-color);
              }
            }
          }

          .form-item-tip {
            margin-top: 8px;

            .el-text {
              color: var(--text-color-placeholder);
            }
          }
        }

        .el-form-item__error {
          color: var(--danger-color);
        }
      }
    }
  }

  .el-dialog__footer {
    background: var(--card-bg);
    border-top: 1px solid var(--border-color-lighter);
    padding: 16px 24px 20px;

    .dialog-footer {
      display: flex;
      justify-content: flex-end;
      gap: 12px;

      .el-button {
        //transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        &:hover {
          transform: translateY(-1px);
        }

        &:active {
          transform: translateY(0);
        }
      }
    }
  }
}

// 新建文档弹窗样式
.upload-steps {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  padding: 0 40px;

  .step-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    position: relative;

    .step-number {
      width: 32px;
      height: 32px;
      border-radius: 50%;
      background: var(--fill-color);
      border: 2px solid var(--border-color);
      color: var(--text-color-placeholder);
      display: flex;
      align-items: center;
      justify-content: center;
      font-weight: 600;
      font-size: 14px;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    .step-title {
      font-size: 14px;
      color: var(--text-color-secondary);
      font-weight: 500;
      transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    &.active {
      .step-number {
        background: var(--primary-color);
        border-color: var(--primary-color);
        color: white;
        transform: scale(1.1);
      }

      .step-title {
        color: var(--primary-color);
        font-weight: 600;
      }
    }

    &.completed {
      .step-number {
        background: var(--success-color);
        border-color: var(--success-color);
        color: white;
      }

      .step-title {
        color: var(--success-color);
      }
    }
  }

  .step-line {
    flex: 1;
    height: 2px;
    background: var(--border-color);
    margin: 0 20px;
    position: relative;
    transition: background 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &.active {
      background: var(--success-color);
    }
  }
}

.step-content {
  min-height: 300px;

  .step-description {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 24px;
    padding: 16px;
    background: var(--fill-color-light);
    border-radius: 8px;
    border-left: 4px solid var(--primary-color);

    .el-icon {
      font-size: 20px;
      color: var(--primary-color);
    }

    span {
      font-size: 16px;
      color: var(--text-color-primary);
      font-weight: 500;
    }
  }
}

// 知识库选择列表样式
.knowledge-base-list {
  max-height: 400px;
  overflow-y: auto;

  .knowledge-base-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 16px;
    margin-bottom: 12px;
    border-radius: 12px;
    background: var(--card-bg);
    border: 2px solid var(--border-color-lighter);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &:hover {
      background: var(--fill-color-light);
      border-color: var(--primary-color-light-7);
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }

    &.selected {
      background: var(--primary-color-light-9);
      border-color: var(--primary-color);

      .knowledge-base-name {
        color: var(--primary-color);
        font-weight: 600;
      }

      .knowledge-base-icon {
        color: var(--primary-color);
      }
    }

    .knowledge-base-info {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;

      .knowledge-base-icon {
        font-size: 24px;
        color: var(--text-color-secondary);
        transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      .knowledge-base-details {
        .knowledge-base-name {
          font-size: 16px;
          font-weight: 500;
          color: var(--text-color-primary);
          margin-bottom: 4px;
          transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }

        .knowledge-base-desc {
          font-size: 14px;
          color: var(--text-color-secondary);
          line-height: 1.4;
        }
      }
    }

    .knowledge-base-meta {
      display: flex;
      align-items: center;
      gap: 12px;

      .selected-icon {
        font-size: 20px;
        color: var(--primary-color);
        animation: checkmark 0.3s ease-in-out;
      }
    }
  }

  .empty-knowledge-bases {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 200px;
    color: var(--text-color-placeholder);
  }
}

@keyframes checkmark {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

// 文件上传区域样式
.file-upload-area {
  margin-bottom: 24px;

  .upload-dropzone {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    border: 2px dashed var(--border-color);
    border-radius: 12px;
    background: var(--fill-color-lighter);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &:hover {
      border-color: var(--primary-color);
      background: var(--primary-color-light-9);
      transform: translateY(-2px);
    }

    .upload-icon {
      font-size: 48px;
      color: var(--primary-color);
      margin-bottom: 16px;
      transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }

    &:hover .upload-icon {
      transform: scale(1.1);
    }

    .upload-text {
      text-align: center;

      p {
        margin: 0;

        &:first-child {
          font-size: 16px;
          color: var(--text-color-primary);
          font-weight: 500;
          margin-bottom: 8px;
        }

        &.upload-hint {
          font-size: 14px;
          color: var(--text-color-secondary);
        }
      }
    }
  }
}

// 文件列表样式
.file-list {
  .file-list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding-bottom: 12px;
    border-bottom: 1px solid var(--border-color-lighter);

    span {
      font-size: 16px;
      font-weight: 600;
      color: var(--text-color-primary);
    }
  }

  .file-items {
    max-height: 300px;
    overflow-y: auto;

    .file-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 12px 16px;
      margin-bottom: 8px;
      border-radius: 8px;
      background: var(--card-bg);
      border: 1px solid var(--border-color-lighter);
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

      &:hover {
        background: var(--fill-color-light);
        border-color: var(--border-color);
      }

      &.uploading {
        border-color: var(--primary-color);
        background: var(--primary-color-light-9);
      }

      &.success {
        border-color: var(--success-color);
        background: var(--success-light);
      }

      &.error {
        border-color: var(--error-color);
        background: var(--error-light);
      }

      &.existing {
        border-color: var(--info-color);
        background: var(--info-light);
      }

      .file-info {
        display: flex;
        align-items: center;
        gap: 12px;
        flex: 1;

        .file-icon {
          font-size: 20px;
          color: var(--primary-color);
        }

        .file-details {
          .file-name {
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color-primary);
            margin-bottom: 4px;
            word-break: break-all;
          }

          .file-size {
            font-size: 12px;
            color: var(--text-color-secondary);
          }
        }
      }

      .file-status {
        display: flex;
        align-items: center;
        gap: 8px;

        .upload-progress {
          width: 120px;
        }

        .success-status {
          display: flex;
          align-items: center;
          gap: 6px;
        }

        .status-icon {
          font-size: 18px;

          &.success-icon {
            color: var(--success-color);
          }

          &.error-icon {
            color: var(--error-color);
          }

          &.existing-icon {
            color: var(--info-color);
          }
        }

        .status-text {
          font-size: 12px;
          font-weight: 500;
          color: var(--success-color);

          &.existing {
            color: var(--info-color);
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .upload-steps {
    padding: 0 20px;

    .step-line {
      margin: 0 10px;
    }
  }

  .step-content {
    .step-description {
      flex-direction: column;
      text-align: center;
      gap: 8px;
    }
  }

  .knowledge-base-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .knowledge-base-meta {
      align-self: flex-end;
    }
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;

    .file-status {
      align-self: flex-end;
    }
  }
}


</style>
