<template>
  <div class="home-container">
    <!-- 页面头部 -->
<!--    <div class="page-header">
      <div class="header-content">
        <div class="title-section">
          <h1 class="page-title">
            <el-icon class="title-icon"><ChatDotRound /></el-icon>
            AI智能对话
          </h1>
          <p class="page-subtitle">基于知识库的智能问答与报告生成系统</p>
        </div>

        &lt;!&ndash; 状态指示器 &ndash;&gt;
        <div class="status-section">
          <div class="mode-indicator">
            <el-tag :type="chatStore.currentMode === 'chat' ? 'primary' : 'warning'" size="large">
              {{ chatStore.modeDisplayName }}
            </el-tag>
          </div>
          <div v-if="chatStore.selectedKnowledgeBase" class="kb-indicator">
            <el-tag type="info" size="small">
              <el-icon><Document /></el-icon>
              {{ getKnowledgeBaseName(chatStore.selectedKnowledgeBase) }}
            </el-tag>
          </div>
        </div>
      </div>
    </div>-->

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 对话区域 -->
      <div class="chat-area">
        <!-- 欢迎界面 -->
        <div v-if="!chatStore.hasMessages" class="welcome-section">
          <div class="welcome-content">
            <div class="welcome-icon">
              <el-icon size="64"><ChatDotRound /></el-icon>
            </div>
            <h2 class="welcome-title">欢迎使用AI智能对话系统</h2>
            <p class="welcome-description">
              选择对话模式，开始您的智能问答之旅
            </p>

            <!-- 功能特性 -->
            <div class="features">
              <div class="feature-item">
                <el-icon class="feature-icon"><ChatDotRound /></el-icon>
                <div class="feature-content">
                  <h3>普通对话</h3>
                  <p>基于知识库的智能问答，支持多轮对话</p>
                </div>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><Document /></el-icon>
                <div class="feature-content">
                  <h3>编报模式</h3>
                  <p>自动生成专业报告，深度分析研究</p>
                </div>
              </div>
              <div class="feature-item">
                <el-icon class="feature-icon"><Connection /></el-icon>
                <div class="feature-content">
                  <h3>流式响应</h3>
                  <p>实时显示AI思考过程，提升交互体验</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 消息列表 -->
        <div v-else class="messages-container" ref="messagesContainer">
          <div class="messages-list">
            <ChatMessage
              v-for="message in chatStore.messages"
              :key="message.id"
              :message="message"
              class="message-item"
            />
          </div>

          <!-- 滚动到底部按钮 -->
          <transition name="fade">
            <el-button
              v-if="showScrollButton"
              class="scroll-to-bottom"
              type="primary"
              circle
              @click="scrollToBottom"
            >
              <el-icon><ArrowDown /></el-icon>
            </el-button>
          </transition>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <ChatInput
          :loading="chatStore.isLoading"
          :disabled="false"
          @send="handleSend"
          @stop="handleStop"
          @clear="handleClear"
          @mode-change="handleModeChange"
          @knowledge-base-change="handleKnowledgeBaseChange"
        />
      </div>
    </div>

    <!-- 加载遮罩 -->
<!--    <div v-if="chatStore.isLoading" class="loading-overlay">-->
<!--      <div class="loading-content">-->
<!--        <el-icon class="loading-icon" size="32"><Loading /></el-icon>-->
<!--        <p class="loading-text">{{ chatStore.currentStatus || '正在处理您的请求...' }}</p>-->
<!--        <el-progress-->
<!--          v-if="chatStore.currentProgress > 0"-->
<!--          :percentage="chatStore.currentProgress"-->
<!--          :show-text="false"-->
<!--          class="loading-progress"-->
<!--        />-->
<!--      </div>-->
<!--    </div>-->
  </div>
</template>

<script setup>
import { ref, nextTick, onMounted, onUnmounted, watch } from 'vue'
import { ElMessage, ElNotification } from 'element-plus'
import {
  ChatDotRound,
  Document,
  Connection,
  ArrowDown,
  Loading
} from '@element-plus/icons-vue'
import { useChatStore } from '@/stores/chat'
import { knowledgeList } from '@/api/knowledge.js'
import { chatWithSSE, generateReportWithSSE, closeSSEConnection } from '@/api/chat'
import ChatMessage from '@/components/chat/ChatMessage.vue'
import ChatInput from '@/components/chat/ChatInput.vue'

// 状态管理
const chatStore = useChatStore()

// 响应式数据
const messagesContainer = ref(null)
const showScrollButton = ref(false)
const knowledgeBases = ref([])

// 方法
const handleSend = async (params) => {
  const { content, mode, knowledgeBase } = params

  try {
    // 添加用户消息
    const userMessageId = chatStore.addUserMessage(content, getKnowledgeBaseName(knowledgeBase))

    // 添加AI消息占位符
    const aiMessageId = chatStore.addAIMessage('', { status: 'generating' })

    // 设置状态
    chatStore.setLoading(true)
    chatStore.setStreamingMessageId(aiMessageId)

    // 滚动到底部
    await nextTick()
    scrollToBottom()

    // 准备API参数
    const apiParams = mode === 'chat'
      ? { question: content, collection_name: knowledgeBase }
      : { title: content, collection_name: knowledgeBase }

    // 创建SSE连接
    const sseConnection = mode === 'chat'
      ? chatWithSSE(
          apiParams,
          (eventType, data) => handleSSEMessage(aiMessageId, eventType, data),
          (error) => handleSSEError(aiMessageId, error),
          (data) => handleSSEComplete(aiMessageId, data)
        )
      : generateReportWithSSE(
          apiParams,
          (eventType, data) => handleSSEMessage(aiMessageId, eventType, data),
          (error) => handleSSEError(aiMessageId, error),
          (data) => handleSSEComplete(aiMessageId, data)
        )

    // 保存SSE连接
    chatStore.setSSEConnection(sseConnection)

  } catch (error) {
    console.error('发送消息失败:', error)
    ElMessage.error('发送消息失败: ' + error.message)
    chatStore.setLoading(false)
  }
}

const handleStop = () => {
  chatStore.stopCurrentChat()
  ElMessage.info('已停止当前对话')
}

const handleClear = () => {
  chatStore.clearMessages()
  ElMessage.success('对话记录已清空')
}

const handleModeChange = (mode) => {
  chatStore.setMode(mode)
  ElMessage.info(`已切换到${mode === 'chat' ? '普通对话' : '编报'}模式`)
}

const handleKnowledgeBaseChange = (knowledgeBase) => {
  chatStore.setKnowledgeBase(knowledgeBase)
  const kbName = getKnowledgeBaseName(knowledgeBase)
  ElMessage.info(`已选择知识库: ${kbName}`)
}

// SSE事件处理
const handleSSEMessage = (messageId, eventType, data) => {
  chatStore.handleSSEMessage(messageId, eventType, data)

  // 更新全局状态
  if (data.status) {
    chatStore.currentStatus = data.status
  }
  if (data.progress !== undefined) {
    chatStore.currentProgress = data.progress
  }

  // 自动滚动到底部
  nextTick(() => {
    scrollToBottom()
  })
}

const handleSSEError = (messageId, error) => {
  console.error('SSE连接错误:', error)

  chatStore.updateAIMessage(messageId, {
    status: 'error',
    error: error.message || '连接错误'
  })

  chatStore.setLoading(false)
  chatStore.setSSEConnection(null)
  chatStore.setStreamingMessageId(null)

  ElNotification({
    title: '连接错误',
    message: 'AI服务连接异常，请稍后重试',
    type: 'error'
  })
}

const handleSSEComplete = (messageId, data) => {
  // console.log('SSE连接完成:', data)

  chatStore.setLoading(false)
  chatStore.setSSEConnection(null)
  chatStore.setStreamingMessageId(null)
  chatStore.currentStatus = ''
  chatStore.currentProgress = 0

  ElNotification({
    title: '回答完成',
    message: 'AI已完成回答生成',
    type: 'success'
  })
}

// 工具方法
const getKnowledgeBaseName = (value) => {
  // console.log('getKnowledgeBaseName', value)
  if (!value) return '全部知识库'
  const kb = knowledgeBases.value.find(item => item.value === value)
  return kb ? kb.label : value
}

const scrollToBottom = () => {
  if (messagesContainer.value) {
    const container = messagesContainer.value
    container.scrollTop = container.scrollHeight
  }
}

const handleScroll = () => {
  if (messagesContainer.value) {
    const container = messagesContainer.value
    const isNearBottom = container.scrollHeight - container.scrollTop - container.clientHeight < 100
    showScrollButton.value = !isNearBottom && chatStore.hasMessages
  }
}

// 加载知识库列表
const loadKnowledgeBases = async () => {
  try {
    const result = await knowledgeList()
    knowledgeBases.value = result.data.map(kb =>{
      return {
        label: kb.name,
        value: kb.name_md5
      }
    })
  } catch (error) {
    console.error('加载知识库列表失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadKnowledgeBases()

  // 监听滚动事件
  if (messagesContainer.value) {
    messagesContainer.value.addEventListener('scroll', handleScroll)
  }
})

onUnmounted(() => {
  // 清理SSE连接
  if (chatStore.currentSSEConnection) {
    closeSSEConnection(chatStore.currentSSEConnection)
  }

  // 移除滚动监听
  if (messagesContainer.value) {
    messagesContainer.value.removeEventListener('scroll', handleScroll)
  }
})

// 监听消息变化，自动滚动
watch(() => chatStore.messages.length, () => {
  nextTick(() => {
    scrollToBottom()
  })
})
</script>

<style lang="scss" scoped>
.home-container {
  height: calc(100vh - 11vh);
  display: flex;
  flex-direction: column;
  background: var(--bg-color-page);
}

.page-header {
  flex-shrink: 0;
  background: var(--bg-color);
  border-bottom: 1px solid var(--border-color-lighter);
  padding: 16px 24px;

  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;

    .title-section {
      .page-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 4px 0;
        font-size: 24px;
        font-weight: 600;
        color: var(--text-color-primary);

        .title-icon {
          color: var(--primary-color);
        }
      }

      .page-subtitle {
        margin: 0;
        font-size: 14px;
        color: var(--text-color-secondary);
      }
    }

    .status-section {
      display: flex;
      align-items: center;
      gap: 12px;

      .mode-indicator {
        .el-tag {
          font-weight: 500;
        }
      }

      .kb-indicator {
        .el-tag {
          display: flex;
          align-items: center;
          gap: 4px;
        }
      }
    }
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  max-width: 1200px;
  margin: 0 auto;
  width: 100%;
  padding: 0 24px;
  overflow: hidden;
}

.chat-area {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  margin-bottom: 16px;
}

.welcome-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;

  .welcome-content {
    text-align: center;
    max-width: 600px;

    .welcome-icon {
      margin-bottom: 24px;
      color: var(--primary-color);
    }

    .welcome-title {
      margin: 0 0 12px 0;
      font-size: 28px;
      font-weight: 600;
      color: var(--text-color-primary);
    }

    .welcome-description {
      margin: 0 0 32px 0;
      font-size: 16px;
      color: var(--text-color-secondary);
      line-height: 1.5;
    }

    .features {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 24px;
      margin-top: 32px;

      .feature-item {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        padding: 20px;
        background: var(--bg-color);
        border-radius: 12px;
        border: 1px solid var(--border-color-lighter);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: var(--box-shadow);
        }

        .feature-icon {
          font-size: 24px;
          color: var(--primary-color);
          flex-shrink: 0;
          margin-top: 2px;
        }

        .feature-content {
          text-align: left;

          h3 {
            margin: 0 0 8px 0;
            font-size: 16px;
            font-weight: 600;
            color: var(--text-color-primary);
          }

          p {
            margin: 0;
            font-size: 14px;
            color: var(--text-color-secondary);
            line-height: 1.4;
          }
        }
      }
    }
  }
}

.messages-container {
  flex: 1;
  position: relative;
  overflow-y: auto;
  padding: 16px 0;

  .messages-list {
    min-height: 100%;

    .message-item {
      animation: fadeInUp 0.3s ease-out;
    }
  }

  .scroll-to-bottom {
    position: absolute;
    bottom: 16px;
    right: 16px;
    z-index: 10;
    box-shadow: var(--box-shadow);
  }
}

.input-area {
  flex-shrink: 0;
  padding: 16px 0 24px 0;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--bg-color-mask);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;

  .loading-content {
    background: var(--bg-color);
    padding: 32px;
    border-radius: 12px;
    text-align: center;
    min-width: 200px;
    box-shadow: var(--box-shadow-lg);

    .loading-icon {
      color: var(--primary-color);
      animation: rotate 1s linear infinite;
      margin-bottom: 16px;
    }

    .loading-text {
      margin: 0 0 16px 0;
      font-size: 14px;
      color: var(--text-color-primary);
    }

    .loading-progress {
      width: 200px;
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 响应式设计
@media (max-width: 768px) {
  .home-container {
    .page-header {
      padding: 12px 16px;

      .header-content {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;

        .title-section {
          .page-title {
            font-size: 20px;
          }

          .page-subtitle {
            font-size: 13px;
          }
        }

        .status-section {
          align-self: stretch;
          justify-content: space-between;
        }
      }
    }

    .main-content {
      padding: 0 16px;
    }

    .welcome-section {
      .welcome-content {
        padding: 0 16px;

        .welcome-title {
          font-size: 24px;
        }

        .welcome-description {
          font-size: 14px;
        }

        .features {
          grid-template-columns: 1fr;
          gap: 16px;

          .feature-item {
            padding: 16px;

            .feature-content {
              h3 {
                font-size: 15px;
              }

              p {
                font-size: 13px;
              }
            }
          }
        }
      }
    }

    .messages-container {
      padding: 12px 0;

      .scroll-to-bottom {
        bottom: 12px;
        right: 12px;
      }
    }

    .input-area {
      padding: 12px 0 16px 0;
    }

    .loading-overlay {
      .loading-content {
        margin: 0 16px;
        padding: 24px;
        min-width: auto;

        .loading-progress {
          width: 150px;
        }
      }
    }
  }
}

@media (max-width: 480px) {
  .home-container {
    .page-header {
      .header-content {
        .status-section {
          flex-direction: column;
          align-items: flex-start;
          gap: 8px;
        }
      }
    }

    .welcome-section {
      .welcome-content {
        .welcome-title {
          font-size: 22px;
        }

        .features {
          .feature-item {
            flex-direction: column;
            text-align: center;

            .feature-content {
              text-align: center;
            }
          }
        }
      }
    }
  }
}
</style>
