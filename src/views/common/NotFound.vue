<template>
  <div class="not-found-page">
    <div class="not-found-content">
      <!-- 404 图标和标题 -->
      <div class="error-visual">
        <div class="error-number">404</div>
        <div class="error-icon">
          <el-icon><DocumentDelete /></el-icon>
        </div>
      </div>

      <!-- 错误信息 -->
      <div class="error-info">
        <h1 class="error-title">页面未找到</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移动。
        </p>
      </div>

      <!-- 操作按钮 -->
      <div class="error-actions">
        <el-button type="primary" size="large" @click="goHome">
          <el-icon><House /></el-icon>
          返回首页
        </el-button>
        <el-button size="large" @click="goBack">
          <el-icon><Back /></el-icon>
          返回上页
        </el-button>
      </div>

      <!-- 建议链接 -->
      <div class="suggestions">
        <h3>您可能想要访问：</h3>
        <div class="suggestion-links">
          <router-link to="/" class="suggestion-link">
            <el-icon><House /></el-icon>
            <span>首页</span>
          </router-link>
          <router-link to="/knowledge" class="suggestion-link">
            <el-icon><FolderOpened /></el-icon>
            <span>知识管理</span>
          </router-link>
          <router-link to="/search" class="suggestion-link">
            <el-icon><Search /></el-icon>
            <span>搜索功能</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="floating-shape shape-1"></div>
      <div class="floating-shape shape-2"></div>
      <div class="floating-shape shape-3"></div>
    </div>
  </div>
</template>

<script setup>
import { useRouter } from 'vue-router'
import {
  DocumentDelete,
  House,
  Back,
  FolderOpened,
  Search
} from '@element-plus/icons-vue'

const router = useRouter()

const goHome = () => {
  router.push('/')
}

const goBack = () => {
  router.go(-1)
}
</script>

<style lang="scss" scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--bg-color-page) 0%, var(--fill-color-light) 100%);
  position: relative;
  overflow: hidden;
  padding: 20px;
}

.not-found-content {
  text-align: center;
  max-width: 600px;
  position: relative;
  z-index: 1;
}

.error-visual {
  position: relative;
  margin-bottom: 40px;
  
  .error-number {
    font-size: 120px;
    font-weight: 900;
    color: var(--primary-color);
    line-height: 1;
    margin-bottom: 20px;
    text-shadow: 0 4px 20px rgba(99, 102, 241, 0.3);
    
    @media (max-width: 768px) {
      font-size: 80px;
    }
  }
  
  .error-icon {
    .el-icon {
      font-size: 64px;
      color: var(--text-color-secondary);
      opacity: 0.6;
    }
  }
}

.error-info {
  margin-bottom: 40px;
  
  .error-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-color-primary);
    margin: 0 0 16px 0;
    
    @media (max-width: 768px) {
      font-size: 24px;
    }
  }
  
  .error-description {
    font-size: 16px;
    color: var(--text-color-secondary);
    line-height: 1.6;
    margin: 0;
  }
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: center;
  }
  
  .el-button {
    height: 48px;
    padding: 0 24px;
    border-radius: 12px;
    font-weight: 600;
    
    &.el-button--primary {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(99, 102, 241, 0.35);
      }
    }
  }
}

.suggestions {
  .suggestion-links {
    display: flex;
    gap: 16px;
    justify-content: center;
    flex-wrap: wrap;
    margin-top: 16px;
    
    .suggestion-link {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px;
      background: var(--card-bg);
      border: 1px solid var(--border-color);
      border-radius: 8px;
      color: var(--text-color-primary);
      text-decoration: none;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      
      &:hover {
        border-color: var(--primary-color);
        background: var(--primary-light);
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.2);
      }
      
      .el-icon {
        font-size: 16px;
      }
      
      span {
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
}

.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  
  .floating-shape {
    position: absolute;
    border-radius: 50%;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    opacity: 0.1;
    animation: float 8s ease-in-out infinite;
    
    &.shape-1 {
      width: 100px;
      height: 100px;
      top: 20%;
      left: 10%;
      animation-delay: 0s;
    }
    
    &.shape-2 {
      width: 60px;
      height: 60px;
      top: 60%;
      right: 20%;
      animation-delay: 2s;
    }
    
    &.shape-3 {
      width: 80px;
      height: 80px;
      bottom: 20%;
      left: 20%;
      animation-delay: 4s;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

// 响应式设计
@media (max-width: 768px) {
  .not-found-page {
    padding: 20px;
  }
  
  .suggestions {
    .suggestion-links {
      flex-direction: column;
      align-items: center;
      
      .suggestion-link {
        width: 200px;
        justify-content: center;
      }
    }
  }
}
</style>
