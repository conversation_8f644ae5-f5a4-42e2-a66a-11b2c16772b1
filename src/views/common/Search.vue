<template>
  <div :class="['modern-search-page', { 'has-results': hasSearched }]">
    <!-- 搜索主区域 -->
    <div :class="['search-hero', { 'search-hero--compact': hasSearched }]">
      <div v-if="!hasSearched" class="hero-background">
        <div class="floating-elements">
          <div class="floating-shape shape-1"></div>
          <div class="floating-shape shape-2"></div>
          <div class="floating-shape shape-3"></div>
          <div class="floating-shape shape-4"></div>
        </div>
      </div>

      <div class="hero-content">
        <!-- 搜索标题 -->
        <div v-if="!hasSearched" class="search-title">
          <h1 class="title-main">
            <el-icon class="title-icon"><Search /></el-icon>
            发现知识
          </h1>
          <p class="title-subtitle">在 {{ totalDocuments }} 个文档中找到您需要的答案</p>
        </div>

        <!-- 主搜索框 -->
        <div class="search-input-container">
          <!-- 搜索后的工具栏 -->
          <div v-if="hasSearched" class="search-toolbar">
            <div class="search-info">
              <div class="search-status">
                <el-icon class="search-status-icon"><Search /></el-icon>
                <span class="search-query-text">{{ currentSearchQuery }}</span>
                <el-divider direction="vertical" />
                <span class="results-summary">
                  {{ searchResults.length }} 个结果
                  <span v-if="searchStats.totalFound > searchResults.length" class="total-found">
                    (共找到 {{ searchStats.totalFound }} 个)
                  </span>
                </span>
                <span v-if="searchTime" class="search-time">{{ searchTime }}ms</span>
              </div>
              <!-- 新搜索提示 -->
              <div v-if="isPreparingNewSearch" class="new-search-hint">
                <el-icon class="hint-icon"><Lightning /></el-icon>
                <span class="hint-text">准备搜索新内容</span>
              </div>
            </div>
            <div class="search-actions-toolbar">
              <!-- 紧凑知识库选择器 -->
              <div class="kb-selector-compact">
                <el-select
                  v-model="selectedKnowledgeBase"
                  size="small"
                  class="kb-select-compact"
                  :loading="isLoadingKnowledgeBases"
                  @change="handleKnowledgeBaseChange"
                >
                  <el-option
                    v-for="kb in knowledgeBases"
                    :key="kb.id"
                    :label="kb.label"
                    :value="kb.id"
                  >
                    <div class="kb-option-compact">
                      <el-icon class="kb-icon-compact"><component :is="kb.icon" /></el-icon>
                      <span class="kb-name-compact">{{ kb.label }}</span>
                      <el-tag size="small" type="info" class="kb-count-compact">{{ kb.count }}</el-tag>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <el-divider direction="vertical" />

              <el-button
                text
                size="small"
                @click="handleClear"
                class="clear-search-btn"
                title="重置搜索状态，返回初始页面"
              >
                <el-icon><RefreshLeft /></el-icon>
                重置
              </el-button>

              <el-divider direction="vertical" />

              <div class="mode-toggle-compact">
                <el-switch
                  v-model="isIntelligentMode"
                  size="small"
                  :active-text="isIntelligentMode ? 'AI' : ''"
                  :inactive-text="!isIntelligentMode ? '文本' : ''"
                  active-color="var(--primary-color)"
                  inactive-color="var(--text-color-secondary)"
                  @change="handleModeChange"
                  class="mode-switch-compact"
                />
              </div>
            </div>
          </div>

          <!-- 集成式搜索控制中心 -->
          <div v-else class="integrated-search-center">
            <!-- 搜索控制面板 -->
            <div class="search-control-panel">
              <!-- 左侧：知识库选择 -->
              <div class="control-section kb-section">
                <div class="section-header">
                  <el-icon class="section-icon"><Collection /></el-icon>
                  <span class="section-title">搜索范围</span>
                </div>
                <el-select
                  v-model="selectedKnowledgeBase"
                  placeholder="选择知识库"
                  size="large"
                  class="kb-select-integrated"
                  :loading="isLoadingKnowledgeBases"
                  @change="handleKnowledgeBaseChange"
                >
                  <el-option
                    v-for="kb in knowledgeBases"
                    :key="kb.id"
                    :label="kb.label"
                    :value="kb.id"
                  >
                    <div class="kb-option-integrated">
                      <el-icon class="kb-icon"><component :is="kb.icon" /></el-icon>
                      <span class="kb-name">{{ kb.label }}</span>
                      <el-tag size="small" class="kb-count">{{ kb.count }}</el-tag>
                    </div>
                  </el-option>
                </el-select>
              </div>

              <!-- 右侧：搜索模式 -->
              <div class="control-section mode-section">
                <div class="section-header">
                  <el-icon class="section-icon"><MagicStick /></el-icon>
                  <span class="section-title">搜索模式</span>
                </div>
                <div class="mode-toggle-integrated">
                  <el-switch
                    v-model="isIntelligentMode"
                    size="large"
                    :active-text="intelligentModeText"
                    :inactive-text="textModeText"
                    active-color="var(--primary-color)"
                    inactive-color="var(--text-color-secondary)"
                    @change="handleModeChange"
                    class="mode-switch-integrated"
                  />
                </div>
              </div>
            </div>

            <!-- 模式描述 -->
            <div class="mode-description-integrated">
              <transition name="fade" mode="out-in">
                <div v-if="isIntelligentMode" key="intelligent" class="mode-desc-card intelligent">
                  <el-icon class="desc-icon"><MagicStick /></el-icon>
                  <span class="desc-text">AI智能搜索，理解语义和上下文，提供更准确的结果</span>
                </div>
                <div v-else key="text" class="mode-desc-card text">
                  <el-icon class="desc-icon"><Document /></el-icon>
                  <span class="desc-text">传统文本搜索，精确关键词匹配，快速定位内容</span>
                </div>
              </transition>
            </div>
          </div>

          <div class="search-input-wrapper">
            <el-input
              v-model="searchQuery"
              :placeholder="searchPlaceholder"
              :size="hasSearched ? 'default' : 'large'"
              :class="[
                'main-search-input',
                isIntelligentMode ? 'intelligent-mode' : 'text-mode',
                {
                  'compact-mode': hasSearched,
                  'preparing-new-search': isPreparingNewSearch
                }
              ]"
              clearable
              @keyup.enter="handleSearch"
              @input="handleSearchInput"
            >
              <template #prefix>
                <el-icon class="search-icon">
                  <MagicStick v-if="isIntelligentMode" />
                  <Search v-else />
                </el-icon>
              </template>
              <template #suffix>
                <div class="search-actions">
                  <el-button
                    type="primary"
                    :loading="isSearching"
                    @click="handleSearch"
                    :size="hasSearched ? 'small' : 'default'"
                    :class="[
                      'search-button',
                      isIntelligentMode ? 'intelligent-mode' : 'text-mode',
                      { 'compact-mode': hasSearched }
                    ]"
                  >
                    <el-icon v-if="!isSearching">
                      <MagicStick v-if="isIntelligentMode" />
                      <Search v-else />
                    </el-icon>
                    <span v-if="!hasSearched">{{ isIntelligentMode ? 'AI搜索' : '搜索' }}</span>
                  </el-button>
                </div>
              </template>
            </el-input>
          </div>
        </div>




      </div>
    </div>



    <!-- 搜索结果区域 -->
    <div class="results-section">
      <!-- 搜索工具栏 -->


      <!-- 搜索状态 -->
      <div v-if="isSearching" class="search-loading">
        <div class="loading-container">
          <div class="loading-animation">
            <div class="loading-spinner"></div>
          </div>
          <p class="loading-text">正在搜索中...</p>
        </div>
      </div>

      <!-- 无搜索结果 -->
      <div v-else-if="hasSearched && searchResults.length === 0" class="no-results">
        <div class="no-results-container">
          <div class="no-results-icon">
            <el-icon><DocumentDelete /></el-icon>
          </div>
          <h3 class="no-results-title">未找到相关内容</h3>
          <p class="no-results-description">
            尝试使用不同的关键词或调整搜索条件
          </p>
          <div class="no-results-actions">
            <el-button type="primary" @click="handleClear">清空搜索</el-button>
          </div>
        </div>
      </div>

      <!-- 搜索结果列表 -->
      <div v-else-if="searchResults.length > 0" class="results-container">
        <div class="results-content view-list">
          <div
            v-for="result in paginatedResults"
            :key="result.id"
            class="result-item"
            @click="viewDocument(result)"
          >
            <div class="result-main">
              <div class="result-header">
                <div class="result-title-row">
                  <h3 class="result-title" v-html="highlightText(result.title)"></h3>
                  <div class="result-score" v-if="result.score">
                    <el-tag size="small" type="success" class="score-tag">
                      匹配度: {{ Math.round(result.score * 100) }}%
                    </el-tag>
                  </div>
                </div>
                <div class="result-meta">
                  <el-tag size="small" :type="getCategoryType(result.category)" class="category-tag">
                    {{ result.categoryName }}
                  </el-tag>
                  <span class="result-author">
                    <el-icon><User /></el-icon>
                    {{ result.author }}
                  </span>
                  <span class="result-date">
                    <el-icon><Calendar /></el-icon>
                    {{ result.updateTime }}
                  </span>
                  <span v-if="result.chunks && result.chunks.length > 0" class="result-chunks">
                    <el-icon><Document /></el-icon>
                    {{ result.chunks.length }} 个匹配片段
                  </span>
                </div>
              </div>

              <div class="result-content">
                <p class="result-excerpt" v-html="highlightText(result.excerpt)"></p>

                <!-- 显示最佳匹配的文档片段 -->
                <div v-if="result.chunks && result.chunks.length > 0" class="result-chunks-preview">
                  <div class="chunks-header">
                    <el-icon><Lightning /></el-icon>
                    <span>最佳匹配片段</span>
                  </div>
                  <div
                    v-for="(chunk, index) in result.chunks.slice(0, 2)"
                    :key="chunk.doc_id || index"
                    class="chunk-item"
                  >
                    <div class="chunk-score">
                      <el-tag size="small" type="info">{{ Math.round(chunk.score * 100) }}%</el-tag>
                    </div>
                    <div class="chunk-text" v-html="highlightText(chunk.payload?.text || '')"></div>
                  </div>
                  <div v-if="result.chunks.length > 2" class="more-chunks">
                    <el-button size="small" text type="primary">
                      查看全部 {{ result.chunks.length }} 个匹配片段
                    </el-button>
                  </div>
                </div>
              </div>

              <div class="result-footer">
                <div class="result-tags" v-if="result.tags && result.tags.length > 0">
                  <el-tag
                    v-for="tag in result.tags.slice(0, 3)"
                    :key="tag"
                    size="small"
                    class="result-tag"
                  >
                    {{ tag }}
                  </el-tag>
                  <span v-if="result.tags.length > 3" class="more-tags">
                    +{{ result.tags.length - 3 }}
                  </span>
                </div>

                <div class="result-actions">
                  <el-button size="small" text type="primary" @click.stop="viewDocument(result)">
                    <el-icon><View /></el-icon>
                    查看
                  </el-button>
                  <el-button size="small" text @click.stop="editDocument(result)">
                    <el-icon><Edit /></el-icon>
                    编辑
                  </el-button>
                  <el-button size="small" text @click.stop="favoriteDocument(result)">
                    <el-icon><Star /></el-icon>
                    收藏
                  </el-button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="searchResults.length"
            layout="total, sizes, prev, pager, next, jumper"
            background
          />
        </div>
      </div>

      <!-- 默认状态：简洁的搜索提示 -->
      <div v-else class="search-welcome">
        <div class="welcome-content">
          <div class="welcome-icon">
            <el-icon><Search /></el-icon>
          </div>
          <h3 class="welcome-title">开始您的知识探索之旅</h3>
          <p class="welcome-description">
            {{ isIntelligentMode ? '使用AI智能搜索，让知识发现更简单' : '输入关键词，精确查找您需要的文档' }}
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import {
  Search,
  Lightning,
  Close,
  DocumentDelete,
  User,
  Calendar,
  View,
  Edit,
  Star,
  Document,
  MagicStick,
  RefreshLeft,
  Collection,
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { fullTextSearch, intelligentSearch } from '@/api/search'
import {knowledgeList} from '@/api/knowledge'

// 响应式数据
const searchQuery = ref('')
const currentSearchQuery = ref('')
const isSearching = ref(false)
const hasSearched = ref(false)
const isPreparingNewSearch = ref(false) // 新增：标识用户是否正在准备新搜索
const currentPage = ref(1)
const pageSize = ref(20)

const searchTime = ref(0)

// 搜索模式相关
const isIntelligentMode = ref(false)
const textModeText = ref('文本搜索')
const intelligentModeText = ref('AI智能搜索')

// 模拟数据 - 需要先定义，因为后面的watch会用到
const totalDocuments = ref(1256)
const searchResults = ref([])

// 知识库选择
const selectedKnowledgeBase = ref('')
const knowledgeBases = ref([])
const isLoadingKnowledgeBases = ref(false)

// 默认的"全部知识库"选项
const defaultKnowledgeBase = {
  id: 'all',
  label: '全部知识库',
  value: '',
  count: 0,
  icon: 'Collection',
  description: '搜索所有知识库内容'
}

// 搜索结果统计
const searchStats = ref({
  totalFound: 0,
  searchTime: 0,
  currentQuery: ''
})

// 确保初始化时设置正确的文档总数
watch(selectedKnowledgeBase, (newValue) => {
  const selectedKB = knowledgeBases.value.find(kb => kb.id === newValue)
  if (selectedKB) {
    totalDocuments.value = selectedKB.count
  }
}, { immediate: true })


// 计算属性
const paginatedResults = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return searchResults.value.slice(start, end)
})

// 当前选中的知识库信息
const selectedKnowledgeBaseInfo = computed(() => {
  return knowledgeBases.value.find(kb => kb.id === selectedKnowledgeBase.value) || knowledgeBases.value[0]
})

// 搜索占位符文本
const searchPlaceholder = computed(() => {
  const kbName = selectedKnowledgeBaseInfo.value?.label || '全部知识库'
  const modeText = isIntelligentMode.value ? 'AI智能搜索' : '文本搜索'
  return `在${kbName}中${modeText}...`
})





// 方法
const handleSearch = async () => {
  if (!searchQuery.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }

  // 智能状态重置：如果是新搜索，重置相关状态
  const isNewSearch = currentSearchQuery.value !== searchQuery.value
  if (isNewSearch) {
    // 重置分页状态
    currentPage.value = 1
  }

  const startTime = Date.now()
  isSearching.value = true
  hasSearched.value = true
  isPreparingNewSearch.value = false // 重置新搜索准备状态
  currentSearchQuery.value = searchQuery.value

  try {
    // 获取当前选中的知识库信息
    const selectedKB = knowledgeBases.value.find(kb => kb.id === selectedKnowledgeBase.value)
    const kbName = selectedKB?.label || '全部知识库'

    // 准备API请求参数
    const searchParams = {
      query: searchQuery.value.trim(),
      collection_name: getCollectionName(selectedKnowledgeBase.value)
    }

    // console.log('搜索参数:', searchParams)

    let apiResponse
    // 根据搜索模式调用不同的API
    if (isIntelligentMode.value) {
      // AI智能搜索
      apiResponse = await intelligentSearch(searchParams)
    } else {
      // 传统文本搜索
      apiResponse = await fullTextSearch(searchParams)
    }

    // console.log('API响应:', apiResponse)

    // 转换API响应数据为组件需要的格式
    const transformedResults = transformSearchResults(apiResponse)
    searchResults.value = transformedResults.results

    // 更新搜索统计信息
    searchStats.value = {
      totalFound: transformedResults.totalFound,
      searchTime: Date.now() - startTime,
      currentQuery: searchQuery.value
    }

    searchTime.value = searchStats.value.searchTime
    currentPage.value = 1

    const modeText = isIntelligentMode.value ? 'AI智能搜索' : '文本搜索'
    const scopeText = selectedKnowledgeBase.value === 'all' ? '' : `在「${kbName}」中`

    if (searchResults.value.length > 0) {
      ElMessage.success(`${modeText}${scopeText}找到 ${searchResults.value.length} 个相关结果`)
    } else {
      ElMessage.info(`${modeText}${scopeText}未找到相关结果，尝试切换搜索模式或知识库`)
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error(`搜索失败: ${error.message || '请重试'}`)
    searchResults.value = []
  } finally {
    isSearching.value = false
  }
}

// 知识库ID映射到collection_name
const getCollectionName = (knowledgeBaseId) => {
  // 如果是"全部知识库"，返回空字符串
  if (knowledgeBaseId === 'all') {
    return ''
  }

  // 从知识库列表中查找对应的value
  const selectedKB = knowledgeBases.value.find(kb => kb.id === knowledgeBaseId)
  return selectedKB?.value || ''
}

// 转换API响应数据为组件需要的格式
const transformSearchResults = (apiResponse) => {
  if (!apiResponse || !apiResponse.results) {
    return {
      results: [],
      totalFound: 0
    }
  }

  const transformedResults = apiResponse.results.map((result, index) => {
    // 从API响应中提取数据
    const metadata = result.metadata || {}
    const firstChunk = result.chunks && result.chunks.length > 0 ? result.chunks[0] : {}
    const payload = firstChunk.payload || {}

    return {
      id: result.file_hash || `result_${index}`,
      file_hash: result.file_hash,
      title: result.file_name || metadata.title || '未知文档',
      excerpt: result.summary || (payload.text ? payload.text.substring(0, 200) + '...' : '暂无摘要'),
      category: 'tech',
      categoryName: '其他文档',
      author: metadata.author || metadata.last_modified_by || '未知作者',
      updateTime: formatDate(metadata.modified || metadata.created),
      tags: [],
      score: result.max_score || 0,
      chunks: result.chunks || [],
      metadata: metadata,
      // 为了兼容现有的UI组件
      fileName: result.file_name,
      summary: result.summary
    }
  }).sort((a, b) => (b.score || 0) - (a.score || 0)) // 按分数降序排列

  return {
    results: transformedResults,
    totalFound: apiResponse.total_found || transformedResults.length
  }
}

// 格式化日期
const formatDate = (dateString) => {
  if (!dateString) return '未知时间'

  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return '未知时间'
  }
}

// 获取知识库列表
const loadKnowledgeBases = async () => {
  isLoadingKnowledgeBases.value = true

  try {
    // console.log('开始获取知识库列表...')
    const apiKnowledgeBases = await knowledgeList()
    // console.log('API返回的知识库数据:', apiKnowledgeBases)

    // 转换API数据格式为组件需要的格式
    const transformedKnowledgeBases = apiKnowledgeBases.data.map((kb, index) => ({
      id: kb.value || `kb_${index}`, // 使用value作为id
      label: kb.name || '未知知识库',
      value: kb.name_md5 || '',
      count: kb.doc_count || 0,
      icon:  'Document', // 根据名称推断图标
      description: kb.description// 根据名称生成描述
    }))

    // 合并默认的"全部知识库"选项和API返回的知识库
    // knowledgeBases.value = [defaultKnowledgeBase, ...transformedKnowledgeBases]
    knowledgeBases.value = transformedKnowledgeBases
    selectedKnowledgeBase.value = knowledgeBases.value[0].id

    // 计算总文档数
    const totalCount = transformedKnowledgeBases.reduce((sum, kb) => sum + kb.count, 0)
    knowledgeBases.value[0].count = totalCount // 更新"全部知识库"的文档数
    totalDocuments.value = totalCount

    // console.log('知识库列表加载完成:', knowledgeBases.value)
    ElMessage.success(`成功加载 ${transformedKnowledgeBases.length} 个知识库`)
  } catch (error) {
    console.error('获取知识库列表失败:', error)
    ElMessage.error('获取知识库列表失败: ' + (error.message || '未知错误'))

    // 失败时使用默认知识库
    knowledgeBases.value = [defaultKnowledgeBase]
  } finally {
    isLoadingKnowledgeBases.value = false
  }
}


// 组件挂载时的初始化
onMounted(async () => {
  // console.log('搜索组件已挂载')
  // console.log('默认选中的知识库:', selectedKnowledgeBase.value)

  // 加载知识库列表
  await loadKnowledgeBases()

  // console.log('知识库列表:', knowledgeBases.value)
  // console.log('当前选中的知识库信息:', selectedKnowledgeBaseInfo.value)
})

// 知识库选择处理
const handleKnowledgeBaseChange = (value) => {
  const selectedKB = knowledgeBases.value.find(kb => kb.id === value)
  // console.log(`知识库切换为: ${selectedKB?.label}`)

  // 更新文档总数显示
  if (selectedKB) {
    totalDocuments.value = selectedKB.count
  }

  // 如果有搜索结果，自动重新搜索
  if (hasSearched.value && searchQuery.value.trim()) {
    ElMessage.info(`已切换到「${selectedKB?.label}」，正在重新搜索...`)
    handleSearch()
  }
}

// 搜索模式切换处理
const handleModeChange = (value) => {
  // console.log(`搜索模式切换为: ${value ? 'AI智能搜索' : '文本搜索'}`)

  // 如果有搜索结果，自动重新搜索
  if (hasSearched.value && searchQuery.value.trim()) {
    ElMessage.info(`已切换到${value ? 'AI智能搜索' : '文本搜索'}模式，正在重新搜索...`)
    handleSearch()
  } else if (searchQuery.value.trim()) {
    // 如果有输入但未搜索，提示用户
    ElMessage.info(`已切换到${value ? 'AI智能搜索' : '文本搜索'}模式`)
  }
}

const handleSearchInput = (value) => {
  // 智能状态管理：当用户开始输入新内容时，自动准备新搜索
  if (hasSearched.value && value !== currentSearchQuery.value) {
    // 用户正在输入新的搜索内容，准备进行新搜索
    // 但不立即清除结果，等用户确认搜索后再清除
    isPreparingNewSearch.value = true
  } else if (value === currentSearchQuery.value) {
    // 用户恢复到当前搜索词，取消新搜索准备状态
    isPreparingNewSearch.value = false
  }
}





const handleClear = () => {
  searchQuery.value = ''
  currentSearchQuery.value = ''
  searchResults.value = []
  hasSearched.value = false
  currentPage.value = 1
}



const highlightText = (text) => {
  if (!currentSearchQuery.value) return text
  const regex = new RegExp(`(${currentSearchQuery.value})`, 'gi')
  return text.replace(regex, '<mark class="search-highlight">$1</mark>')
}

const getCategoryType = (category) => {
  const types = {
    tech: 'primary',
    product: 'success',
    manual: 'warning',
    api: 'info'
  }
  return types[category] || 'info'
}



const viewDocument = (doc) => {
  // console.log('查看文档详情:', doc)

  // 显示文档详细信息的弹窗
  ElMessageBox.alert(
    `
    <div style="text-align: left;">
      <h3>${doc.title}</h3>
      <p><strong>文件哈希:</strong> ${doc.file_hash}</p>
      <p><strong>分类:</strong> ${doc.categoryName}</p>
      <p><strong>作者:</strong> ${doc.author}</p>
      <p><strong>更新时间:</strong> ${doc.updateTime}</p>
      <p><strong>匹配分数:</strong> ${doc.score ? Math.round(doc.score * 100) + '%' : '无'}</p>
      <p><strong>摘要:</strong></p>
      <div style="max-height: 200px; overflow-y: auto; background: #f5f5f5; padding: 10px; border-radius: 4px;">
        ${doc.excerpt}
      </div>
      ${doc.chunks && doc.chunks.length > 0 ? `
        <p><strong>匹配片段数:</strong> ${doc.chunks.length}</p>
      ` : ''}
    </div>
    `,
    '文档详情',
    {
      dangerouslyUseHTMLString: true,
      customClass: 'document-detail-dialog'
    }
  )
}

const editDocument = (doc) => {
  ElMessage.info(`编辑文档: ${doc.title}`)
  // 这里可以跳转到文档编辑页
  // router.push(`/document/${doc.file_hash}/edit`)
}

const favoriteDocument = (doc) => {
  ElMessage.success(`已收藏文档: ${doc.title}`)
  // 这里可以调用收藏API
}
</script>

<style lang="scss" scoped>
.modern-search-page {
  //height: calc(100vh - 60px);
  overflow-y: auto;
  background: linear-gradient(135deg,
    var(--bg-color-page) 0%,
    rgba(99, 102, 241, 0.02) 50%,
    var(--bg-color-page) 100%);

  // 未搜索状态：确保内容适应视口，无滚动
  &:not(.has-results) {
    height: calc(100vh - 11vh);
    overflow: hidden;
  }

  // 有搜索结果状态：允许滚动查看结果
  &.has-results {
    height: calc(100vh - 11vh);
    overflow-y: auto;
  }
}

// ==================== 搜索主区域 ====================
.search-hero {
  position: relative;
  background: linear-gradient(135deg,
    rgba(99, 102, 241, 0.05) 0%,
    rgba(139, 92, 246, 0.03) 100%);
  overflow: hidden;
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  // 未搜索状态：使用flex布局，充分利用可用空间
  .modern-search-page:not(.has-results) & {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 60px);
    padding: 40px 24px 20px;
  }

  // 搜索后状态：紧凑布局
  .modern-search-page.has-results & {
    padding: 24px 24px 20px;
  }

  // 搜索后的紧凑模式
  &.search-hero--compact {
    background: var(--card-bg);
    border-bottom: 1px solid var(--border-color-lighter);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    min-height: auto;
    flex: none;

    .hero-content {
      max-width: 100%;
      text-align: left;
    }

    .hero-content {
      max-width: 100%;
    }

    .search-mode-toggle {
      margin-bottom: 16px;

      .mode-toggle-wrapper {
        padding: 12px 20px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
      }
    }

    .search-input-container {
      margin-bottom: 0;

      .search-input-wrapper {
        .main-search-input {
          :deep(.el-input__wrapper) {
            height: 48px;
            border-radius: 24px;
          }

          :deep(.el-input__inner) {
            font-size: 16px;
          }

          :deep(.el-input__suffix) {
            .search-actions {
              .search-button {
                height: 36px;
                padding: 0 16px;
                font-size: 14px;
              }
            }
          }
        }
      }
    }
  }

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;

    .floating-elements {
      position: relative;
      width: 100%;
      height: 100%;

      .floating-shape {
        position: absolute;
        border-radius: 50%;
        background: linear-gradient(135deg,
          rgba(99, 102, 241, 0.1) 0%,
          rgba(139, 92, 246, 0.05) 100%);
        animation: float 8s ease-in-out infinite;

        &.shape-1 {
          width: 120px;
          height: 120px;
          top: 10%;
          left: 10%;
          animation-delay: 0s;
        }

        &.shape-2 {
          width: 80px;
          height: 80px;
          top: 20%;
          right: 15%;
          animation-delay: 2s;
        }

        &.shape-3 {
          width: 100px;
          height: 100px;
          bottom: 30%;
          left: 20%;
          animation-delay: 4s;
        }

        &.shape-4 {
          width: 60px;
          height: 60px;
          bottom: 20%;
          right: 25%;
          animation-delay: 6s;
        }
      }
    }
  }

  .hero-content {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
    text-align: center;

    // 未搜索状态：使用flex布局，垂直居中
    .modern-search-page:not(.has-results) & {
      display: flex;
      flex-direction: column;
      justify-content: center;
      flex: 1;
      min-height: 0;
    }
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.7;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

// ==================== 搜索标题 ====================
.search-title {
  margin-bottom: 40px;

  .title-main {
    font-size: 48px;
    font-weight: 700;
    color: var(--text-color-primary);
    margin: 0 0 16px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 16px;

    .title-icon {
      font-size: 52px;
      color: var(--primary-color);
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    @media (max-width: 768px) {
      font-size: 36px;
      flex-direction: column;
      gap: 8px;

      .title-icon {
        font-size: 40px;
      }
    }
  }

  .title-subtitle {
    font-size: 18px;
    color: var(--text-color-secondary);
    margin: 0;
    font-weight: 400;

    @media (max-width: 768px) {
      font-size: 16px;
    }
  }
}

// ==================== 集成式搜索控制中心 ====================
.integrated-search-center {
  margin-bottom: 32px;
  max-width: 800px;
  margin-left: auto;
  margin-right: auto;
}

.search-control-panel {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 16px;
  padding: 24px;
  background: var(--card-bg);
  border-radius: 20px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.06);
  border: 1px solid var(--border-color-lighter);
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  align-items: stretch; // 确保两个区域高度一致

  &:hover {
    box-shadow: 0 12px 48px rgba(0, 0, 0, 0.08);
    transform: translateY(-2px);
  }

  .control-section {
    display: flex;
    flex-direction: column;
    min-height: 120px; // 确保两个区域有相同的最小高度
    justify-content: flex-start; // 从顶部开始对齐
    position: relative; // 为了更好的控制子元素位置

    .section-header {
      display: flex;
      align-items: center;
      gap: 8px;
      height: 32px; // 固定标题区域高度
      margin-bottom: 12px;

      .section-icon {
        font-size: 18px;
        color: var(--primary-color);
        flex-shrink: 0; // 防止图标被压缩
      }

      .section-title {
        font-size: 14px;
        font-weight: 600;
        color: var(--text-color-primary);
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        line-height: 1.2; // 统一行高
      }
    }

    &.kb-section {

      .kb-select-integrated {
        width: 100%;
        flex: 1; // 占用剩余空间，确保垂直对齐
        //display: flex;
        align-items: center; // 垂直居中对齐
        margin-top: 3vh;

        :deep(.el-select) {
          width: 100%;
        }

        :deep(.el-select__wrapper) {
          border-radius: 12px;
          padding: 12px 16px;
          border: 1px solid var(--border-color-light);
          background: var(--fill-color-extra-light);
          transition: all 0.3s ease;
          min-height: 48px; // 确保最小高度一致

          &:hover {
            border-color: var(--primary-color);
            background: var(--primary-lighter);
          }

          &.is-focus {
            box-shadow: 0 0 0 2px var(--primary-light);
            border-color: var(--primary-color);
          }
        }

        :deep(.el-input__inner) {
          font-size: 15px;
          font-weight: 500;
          color: var(--text-color-primary) !important; // 强制主题色彩适配
          line-height: 1.4; // 统一行高
          background: transparent !important; // 确保背景透明
        }

        // 选中值前缀图标样式
        :deep(.el-input__prefix) {
          .selected-kb-display {
            display: flex;
            align-items: center;
            margin-right: 8px;

            .selected-kb-icon {
              font-size: 16px;
              color: var(--primary-color);
            }
          }
        }

        // 下拉箭头样式
        :deep(.el-input__suffix) {
          .el-input__suffix-inner {
            .el-select__caret {
              color: var(--text-color-secondary);
              transition: color 0.3s ease;
            }
          }

          &:hover .el-input__suffix-inner .el-select__caret {
            color: var(--primary-color);
          }
        }
      }
    }

    &.mode-section {
      .mode-toggle-integrated {
        display: flex;
        justify-content: center;
        align-items: center; // 垂直居中对齐
        padding: 12px;
        background: var(--fill-color-extra-light);
        border-radius: 12px;
        border: 1px solid var(--border-color-light);
        transition: all 0.3s ease;
        flex: 1; // 占用剩余空间，确保垂直对齐
        min-height: 48px; // 与选择器保持相同的最小高度

        &:hover {
          background: var(--primary-lighter);
          border-color: var(--primary-light);
        }

        .mode-switch-integrated {
          :deep(.el-switch__label) {
            font-weight: 600;
            font-size: 14px;
            line-height: 1.4; // 统一行高
            
          }
          
          :deep(.el-switch__core) {
            vertical-align: middle; // 确保开关垂直居中
          }
          
        }
      }
    }
  }
}

.mode-description-integrated {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;

  .mode-desc-card {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 12px 20px;
    background: var(--fill-color-extra-light);
    border-radius: 12px;
    border: 1px solid var(--border-color-lighter);
    max-width: 400px;
    transition: all 0.3s ease;

    .desc-icon {
      font-size: 16px;
      flex-shrink: 0;
    }

    .desc-text {
      font-size: 13px;
      line-height: 1.4;
      color: var(--text-color-regular);
    }

    &.intelligent {
      .desc-icon {
        color: var(--primary-color);
      }

      border-color: var(--primary-light);
      background: var(--primary-lighter);
    }

    &.text {
      .desc-icon {
        color: var(--success-color);
      }

      border-color: var(--success-light-7);
      background: var(--success-light-9);
    }
  }
}

// ==================== 知识库选择器 ====================
.knowledge-base-selector {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  width: 100%;
  max-width: 600px;

  .selector-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color-primary);

    .label-icon {
      font-size: 18px;
      color: var(--primary-color);
    }

    .label-text {
      background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }
  }

  .kb-select {
    width: 100%;

    :deep(.el-input__wrapper) {
      border-radius: 16px;
      padding: 12px 16px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
      border: 1px solid var(--border-color-lighter);
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      background: var(--card-bg);

      &:hover {
        box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
        transform: translateY(-1px);
        border-color: var(--primary-color);
      }

      &.is-focus {
        box-shadow: 0 0 0 2px var(--primary-light);
        border-color: var(--primary-color);
      }
    }

    :deep(.el-input__inner) {
      font-size: 16px;
      color: var(--text-color-primary);
      font-weight: 500;
    }

    :deep(.el-input__suffix) {
      .el-input__suffix-inner {
        .el-select__caret {
          color: var(--primary-color);
          font-size: 14px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    max-width: 100%;

    .selector-label {
      font-size: 14px;

      .label-icon {
        font-size: 16px;
      }
    }
  }
}

// 对齐优化补充样式
.search-control-panel {
  // 确保网格项目完全对齐
  .control-section {
    // 使用flexbox确保内容对齐
    &::before {
      content: '';
      display: block;
      height: 0;
      visibility: hidden;
    }

    // 确保两个区域的内容区域高度一致
    .kb-select-integrated,
    .mode-toggle-integrated {
      box-sizing: border-box;

      // 确保所有交互元素都有相同的基础样式
      * {
        box-sizing: border-box;
      }
    }
  }

  // 网格对齐优化
  &::after {
    content: '';
    display: block;
    clear: both;
  }
}

// 基线对齐优化
.section-header {
  // 确保标题区域的基线对齐
  align-items: baseline !important;

  .section-icon,
  .section-title {
    vertical-align: baseline;
  }
}

// Element Plus 组件对齐修正
:deep(.el-select) {
  .el-input {
    .el-input__wrapper {
      align-items: center !important;
    }
  }
}

:deep(.el-switch) {
  vertical-align: middle;

  .el-switch__core {
    vertical-align: middle;
  }

  .el-switch__label {
    vertical-align: middle;
  }
}

// 开发调试：对齐辅助线（生产环境请移除）
// .search-control-panel {
//   .control-section {
//     border: 1px dashed rgba(255, 0, 0, 0.3);
//
//     .section-header {
//       border: 1px dashed rgba(0, 255, 0, 0.3);
//     }
//
//     .kb-select-integrated,
//     .mode-toggle-integrated {
//       border: 1px dashed rgba(0, 0, 255, 0.3);
//     }
//   }
// }

// 集成式知识库选项样式 - 主题适配
.kb-option-integrated {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-height: 24px; // 确保最小高度一致

  .kb-icon {
    font-size: 16px;
    color: var(--primary-color);
    flex-shrink: 0;
    transition: color 0.2s ease;
    width: 16px; // 固定宽度确保对齐
    height: 16px; // 固定高度确保对齐
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .kb-name {
    flex: 1;
    font-weight: 500;
    font-size: 14px;
    color: var(--text-color-primary);
    transition: color 0.2s ease;
    line-height: 1.4; // 统一行高
    display: flex;
    align-items: center;
  }

  .kb-count {
    font-size: 12px;
    background: var(--primary-lighter);
    color: var(--primary-color);
    border: 1px solid var(--primary-light);
    border-radius: 8px;
    padding: 2px 6px;
    font-weight: 500;
    transition: all 0.2s ease;
    flex-shrink: 0; // 防止压缩
    min-width: 24px; // 最小宽度
    text-align: center; // 居中对齐
    line-height: 1.2; // 统一行高
  }
}

// 选中值显示样式
.selected-kb-display {
  display: flex;
  align-items: center;
  gap: 6px;

  .selected-kb-icon {
    font-size: 16px;
    color: var(--primary-color);
  }
}


.kb-option-content {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .kb-option-main {
    display: flex;
    align-items: center;
    gap: 8px;

    .kb-icon {
      font-size: 16px;
      color: var(--primary-color);
      flex-shrink: 0;
    }

    .kb-name {
      flex: 1;
      font-weight: 500;
      font-size: 14px;
    }

    .kb-count {
      font-size: 12px;
      background: var(--primary-lighter);
      color: var(--primary-color);
      border: 1px solid var(--primary-light);
      border-radius: 10px;
      padding: 2px 6px;
      font-weight: 500;
    }
  }

  .kb-description {
    font-size: 12px;
    color: var(--text-color-secondary);
    margin-left: 24px;
    line-height: 1.4;
  }
}

// ==================== 搜索模式切换 ====================
.search-mode-toggle {
  margin-bottom: 24px;
  display: flex;
  justify-content: center;

  .mode-toggle-wrapper {
    background: var(--card-bg);
    border-radius: 16px;
    padding: 20px 32px;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.06);
    border: 1px solid var(--border-color-lighter);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

    &:hover {
      box-shadow: 0 6px 24px rgba(0, 0, 0, 0.08);
      transform: translateY(-2px);
    }

    .mode-switch {
      margin-bottom: 12px;

      :deep(.el-switch__label) {
        font-weight: 600;
        font-size: 14px;

        &.is-active {
          color: var(--primary-color);
        }
      }

      :deep(.el-switch__core) {
        width: 60px;
        height: 28px;
        border-radius: 14px;

        &::after {
          width: 24px;
          height: 24px;
          border-radius: 12px;
        }
      }
    }

    .mode-description {
      text-align: center;
      min-height: 24px;

      .mode-desc {
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 6px;
        font-size: 13px;
        font-weight: 500;
        transition: all 0.3s ease;

        .el-icon {
          font-size: 14px;
        }

        &.intelligent {
          color: var(--primary-color);

          .el-icon {
            color: var(--primary-color);
          }
        }

        &.text {
          color: var(--text-color-secondary);

          .el-icon {
            color: var(--text-color-secondary);
          }
        }
      }
    }
  }

  @media (max-width: 768px) {
    .mode-toggle-wrapper {
      padding: 16px 24px;

      .mode-switch {
        :deep(.el-switch__label) {
          font-size: 13px;
        }
      }

      .mode-description {
        .mode-desc {
          font-size: 12px;
        }
      }
    }
  }
}

// ==================== 搜索输入框 ====================
.search-input-container {
  margin-bottom: 32px;

  .search-input-wrapper {
    position: relative;

    .main-search-input {
      :deep(.el-input__wrapper) {
        height: 64px;
        border-radius: 32px;
        border: 2px solid transparent;
        background: var(--card-bg);
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

        // 根据搜索模式显示不同的阴影效果
        &.intelligent-mode {
          box-shadow: 0 8px 32px rgba(139, 92, 246, 0.15);

          &:hover {
            box-shadow: 0 12px 40px rgba(139, 92, 246, 0.2);
            transform: translateY(-2px);
          }

          &.is-focus {
            border-color: var(--primary-color);
            box-shadow: 0 12px 40px rgba(139, 92, 246, 0.25);
            transform: translateY(-2px);
          }
        }

        &.text-mode {
          box-shadow: 0 8px 32px rgba(99, 102, 241, 0.15);

          &:hover {
            box-shadow: 0 12px 40px rgba(99, 102, 241, 0.2);
            transform: translateY(-2px);
          }

          &.is-focus {
            border-color: var(--primary-color);
            box-shadow: 0 12px 40px rgba(99, 102, 241, 0.25);
            transform: translateY(-2px);
          }
        }
      }

      :deep(.el-input__inner) {
        font-size: 18px;
        padding: 0 24px;
        color: var(--text-color-primary);

        &::placeholder {
          color: var(--text-color-placeholder);
          font-weight: 400;
        }
      }

      :deep(.el-input__prefix) {
        left: 24px;

        .search-icon {
          font-size: 20px;
          color: var(--text-color-secondary);
        }
      }

      :deep(.el-input__suffix) {
        right: 8px;

        .search-actions {
          .search-button {
            height: 48px;
            padding: 0 24px;
            border-radius: 24px;
            font-weight: 600;
            font-size: 16px;
            border: none;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

            // 智能搜索模式的渐变
            &.intelligent-mode {
              background: linear-gradient(135deg, #8B5CF6 0%, #A855F7 100%);

              &:hover {
                background: linear-gradient(135deg, #7C3AED 0%, #9333EA 100%);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(139, 92, 246, 0.4);
              }
            }

            // 文本搜索模式的渐变
            &.text-mode {
              background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);

              &:hover {
                background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
                transform: translateY(-1px);
                box-shadow: 0 6px 20px rgba(99, 102, 241, 0.3);
              }
            }

            &:active {
              transform: translateY(0);
            }
          }
        }
      }
    }
  }
}

// ==================== 搜索建议下拉 ====================
.search-suggestions {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: var(--card-bg);
  border-radius: 16px;
  box-shadow: 0 12px 48px rgba(0, 0, 0, 0.12);
  border: 1px solid var(--border-color-light);
  z-index: 1000;
  margin-top: 8px;
  overflow: hidden;

  .suggestion-section {
    &:not(:last-child) {
      border-bottom: 1px solid var(--border-color-lighter);
    }

    .suggestion-header {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 12px 20px 8px;
      font-size: 14px;
      font-weight: 600;
      color: var(--text-color-secondary);

      .el-button {
        margin-left: auto;
        font-size: 12px;
      }
    }

    .suggestion-list {
      .suggestion-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px 20px;
        cursor: pointer;
        transition: all 0.2s ease;

        &:hover {
          background: var(--fill-color-light);
        }

        .el-icon {
          font-size: 16px;
          color: var(--text-color-secondary);
        }

        .suggestion-text {
          flex: 1;
          font-size: 14px;
          color: var(--text-color-primary);
        }

        .suggestion-count {
          font-size: 12px;
          color: var(--text-color-secondary);
        }


      }
    }
  }
}

// ==================== 搜索工具栏（搜索后状态） ====================
.search-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
  padding: 12px 16px;
  background: var(--fill-color-extra-light);
  border-radius: 12px;
  border: 1px solid var(--border-color-lighter);

  .search-info {
    flex: 1;

    .search-status {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 14px;

      .search-status-icon {
        color: var(--primary-color);
        font-size: 16px;
      }

      .search-query-text {
        font-weight: 600;
        color: var(--text-color-primary);
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .results-summary {
        color: var(--text-color-secondary);
        font-size: 13px;
      }

      .search-time {
        color: var(--text-color-placeholder);
        font-size: 12px;
      }

      .el-divider {
        margin: 0 8px;
        height: 14px;
      }
    }

    .new-search-hint {
      display: flex;
      align-items: center;
      gap: 6px;
      margin-top: 8px;
      padding: 6px 12px;
      background: var(--success-light-9);
      border: 1px solid var(--success-light-7);
      border-radius: 8px;
      font-size: 12px;
      color: var(--success-color);
      animation: slideInDown 0.3s ease;

      .hint-icon {
        font-size: 14px;
      }

      .hint-text {
        font-weight: 500;
      }
    }
  }

  .search-actions-toolbar {
    display: flex;
    align-items: center;
    gap: 8px;

    // 紧凑知识库选择器
    .kb-selector-compact {
      .kb-select-compact {
        width: 140px;

        :deep(.el-input__wrapper) {
          border-radius: 8px;
          padding: 4px 8px;
          box-shadow: none;
          border: 1px solid var(--border-color-light);
          background: var(--fill-color-light);
          transition: all 0.2s ease;

          &:hover {
            border-color: var(--primary-color);
            background: var(--primary-lighter);
          }

          &.is-focus {
            box-shadow: 0 0 0 1px var(--primary-light);
            border-color: var(--primary-color);
          }
        }

        :deep(.el-input__inner) {
          font-size: 13px;
          color: var(--text-color-primary);
          font-weight: 500;
        }

        :deep(.el-input__suffix) {
          .el-input__suffix-inner {
            .el-select__caret {
              color: var(--text-color-secondary);
              font-size: 12px;
            }
          }
        }
      }
    }

    .clear-search-btn {
      font-size: 13px;
      color: var(--text-color-secondary);

      &:hover {
        color: var(--primary-color);
      }
    }

    .mode-toggle-compact {
      .mode-switch-compact {
        :deep(.el-switch__label) {
          font-size: 12px;
        }
      }
    }

    .el-divider {
      margin: 0 8px;
      height: 16px;
    }
  }
}

// 紧凑知识库选项样式
.kb-option-compact {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
  min-height: 20px; // 确保最小高度一致

  .kb-icon-compact {
    font-size: 14px;
    color: var(--primary-color);
    flex-shrink: 0;
    width: 14px; // 固定宽度确保对齐
    height: 14px; // 固定高度确保对齐
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .kb-name-compact {
    flex: 1;
    font-size: 13px;
    font-weight: 500;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.4; // 统一行高
    display: flex;
    align-items: center;
  }

  .kb-count-compact {
    font-size: 11px;
    padding: 1px 4px;
    border-radius: 6px;
    background: var(--fill-color-light);
    color: var(--text-color-secondary);
    border: none;
    font-weight: 500;
    flex-shrink: 0; // 防止压缩
    min-width: 20px; // 最小宽度
    text-align: center; // 居中对齐
    line-height: 1.2; // 统一行高
  }
}
:deep(.el-input__wrapper) {
  height: 50px !important;
  border-radius: 20px !important;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
  transition: all 0.3s ease !important;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
    transform: none !important;
    border-color: var(--primary-color) !important;
  }

  &.is-focus {
    box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2) !important;
    transform: none !important;
    border-color: var(--primary-color) !important;
  }
}


// ==================== 紧凑模式搜索框样式 ====================
.main-search-input.compact-mode {
  :deep(.el-input__wrapper) {
    height: 40px !important;
    border-radius: 20px !important;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12) !important;
      transform: none !important;
      border-color: var(--primary-color) !important;
    }

    &.is-focus {
      box-shadow: 0 4px 12px rgba(var(--primary-color-rgb), 0.2) !important;
      transform: none !important;
      border-color: var(--primary-color) !important;
    }
  }

  // 准备新搜索状态的视觉提示
  &.preparing-new-search {
    :deep(.el-input__wrapper) {
      border-color: var(--success-color) !important;
      box-shadow: 0 2px 8px rgba(var(--success-color-rgb), 0.15) !important;

      &:hover, &.is-focus {
        border-color: var(--success-color) !important;
        box-shadow: 0 4px 12px rgba(var(--success-color-rgb), 0.25) !important;
      }
    }

    :deep(.el-input__inner) {
      color: var(--success-color) !important;
    }
  }

  :deep(.el-input__inner) {
    font-size: 14px !important;
    padding: 0 16px !important;
  }

  :deep(.el-input__prefix) {
    left: 16px !important;

    .search-icon {
      font-size: 16px !important;
    }
  }

  :deep(.el-input__suffix) {
    right: 6px !important;

    .search-actions {
      .search-button.compact-mode {
        height: 28px !important;
        padding: 0 12px !important;
        border-radius: 14px !important;
        font-size: 12px !important;
        min-width: 32px !important;

        .el-icon {
          font-size: 14px !important;
        }

        &:hover {
          transform: none !important;
          box-shadow: 0 2px 8px rgba(var(--primary-color-rgb), 0.3) !important;
        }
      }
    }
  }
}

// ==================== 动画效果 ====================
@keyframes slideInDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
}







// ==================== 搜索结果区域 ====================
.results-section {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px 24px 40px;

  // 只在有搜索结果时设置最小高度
  .modern-search-page.has-results & {
    //min-height: calc(100vh - 11vh);
    padding-top: 0;
  }
}



// ==================== 加载状态 ====================
.search-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;

  .loading-container {
    text-align: center;

    .loading-animation {
      margin-bottom: 16px;

      .loading-spinner {
        width: 48px;
        height: 48px;
        border: 4px solid var(--border-color-lighter);
        border-top: 4px solid var(--primary-color);
        border-radius: 50%;
        animation: spin 1s linear infinite;
        margin: 0 auto;
      }
    }

    .loading-text {
      font-size: 16px;
      color: var(--text-color-secondary);
      margin: 0;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

// ==================== 无结果状态 ====================
.no-results {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;

  .no-results-container {
    text-align: center;
    max-width: 400px;

    .no-results-icon {
      margin-bottom: 24px;

      .el-icon {
        font-size: 80px;
        color: var(--text-color-placeholder);
      }
    }

    .no-results-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-color-primary);
      margin: 0 0 12px 0;
    }

    .no-results-description {
      font-size: 16px;
      color: var(--text-color-secondary);
      margin: 0 0 32px 0;
      line-height: 1.5;
    }

    .no-results-actions {
      display: flex;
      gap: 12px;
      justify-content: center;
      flex-wrap: wrap;
    }
  }
}

// ==================== 搜索结果列表 ====================
.results-container {
  .results-content {
    display: grid;
    gap: 16px;
    margin-bottom: 32px;

    grid-template-columns: 1fr;

    .result-item {
      background: var(--card-bg);
      border-radius: 12px;
      padding: 24px;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      border: 1px solid var(--border-color-lighter);

      &:hover {
        transform: translateY(-4px);
        box-shadow: 0 12px 32px rgba(0, 0, 0, 0.08);
        border-color: var(--primary-color);
      }

      .result-main {
        .result-header {
          margin-bottom: 12px;

          .result-title {
            font-size: 18px;
            font-weight: 600;
            color: var(--text-color-primary);
            margin: 0 0 8px 0;
            line-height: 1.4;

            :deep(.search-highlight) {
              background: linear-gradient(120deg, var(--primary-light) 0%, var(--primary-color) 100%);
              color: var(--primary-color);
              padding: 2px 4px;
              border-radius: 4px;
              font-weight: 700;
            }
          }

          .result-meta {
            display: flex;
            align-items: center;
            gap: 12px;
            flex-wrap: wrap;

            .category-tag {
              font-size: 12px;
              border-radius: 12px;
            }

            .result-author,
            .result-date {
              display: flex;
              align-items: center;
              gap: 4px;
              font-size: 13px;
              color: var(--text-color-secondary);

              .el-icon {
                font-size: 12px;
              }
            }
          }
        }

        .result-content {
          margin-bottom: 16px;

          .result-excerpt {
            font-size: 14px;
            color: var(--text-color-regular);
            line-height: 1.6;
            margin: 0;

            :deep(.search-highlight) {
              background: linear-gradient(120deg, var(--primary-light) 0%, var(--primary-color) 100%);
              color: var(--primary-color);
              padding: 1px 3px;
              border-radius: 3px;
              font-weight: 600;
            }
          }
        }

        .result-footer {
          display: flex;
          align-items: center;
          justify-content: space-between;

          .result-tags {
            display: flex;
            align-items: center;
            gap: 6px;
            flex-wrap: wrap;

            .result-tag {
              font-size: 11px;
              border-radius: 8px;
              background: var(--fill-color-lighter);
              border: none;
            }

            .more-tags {
              font-size: 11px;
              color: var(--text-color-placeholder);
            }
          }

          .result-actions {
            display: flex;
            gap: 8px;

            .el-button {
              font-size: 12px;
              padding: 4px 8px;

              .el-icon {
                font-size: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// ==================== 分页 ====================
.pagination-container {
  display: flex;
  justify-content: center;
  padding: 24px 0;

  :deep(.el-pagination) {
    .el-pager li {
      border-radius: 8px;
      margin: 0 2px;

      &.is-active {
        background: var(--primary-color);
        color: white;
      }
    }

    .btn-prev,
    .btn-next {
      border-radius: 8px;
    }
  }
}

// ==================== 搜索欢迎区域 ====================
.search-welcome {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;

  // 在未搜索状态下，让欢迎区域自适应高度
  .modern-search-page:not(.has-results) & {
    flex: 1;
    min-height: 0;
  }

  .welcome-content {
    text-align: center;
    max-width: 500px;

    .welcome-icon {
      margin-bottom: 24px;

      .el-icon {
        font-size: 64px;
        color: var(--text-color-placeholder);
        opacity: 0.6;
      }
    }

    .welcome-title {
      font-size: 24px;
      font-weight: 600;
      color: var(--text-color-primary);
      margin: 0 0 16px 0;
      line-height: 1.4;
    }

    .welcome-description {
      font-size: 16px;
      color: var(--text-color-secondary);
      margin: 0;
      line-height: 1.6;
    }
  }

  @media (max-width: 768px) {
    padding: 16px;

    .welcome-content {
      .welcome-icon {
        margin-bottom: 16px;

        .el-icon {
          font-size: 48px;
        }
      }

      .welcome-title {
        font-size: 20px;
      }

      .welcome-description {
        font-size: 14px;
      }
    }
  }
}

// ==================== 过渡动画 ====================
.fade-enter-active,
.fade-leave-active {
  transition: all 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
  transform: translateY(-10px);
}

// ==================== 响应式设计 ====================
@media (max-width: 768px) {
  .modern-search-page {
    .search-hero {
      // 未搜索状态的移动端适配
      &:not(.search-hero--compact) {
        padding: 20px 16px 16px;
      }

      // 紧凑模式的移动端适配
      &.search-hero--compact {
        padding: 16px;

        .search-mode-toggle {
          margin-bottom: 12px;

          .mode-toggle-wrapper {
            padding: 10px 16px;
          }
        }
      }

      .search-input-container {
        // 搜索工具栏移动端适配
        .search-toolbar {
          flex-direction: column;
          gap: 12px;
          padding: 12px;

          .search-info {
            width: 100%;

            .search-status {
              flex-wrap: wrap;
              gap: 6px;
              font-size: 13px;

              .search-query-text {
                max-width: 150px;
              }
            }
          }

          .search-actions-toolbar {
            width: 100%;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 8px;

            .kb-selector-compact {
              .kb-select-compact {
                width: 120px;

                :deep(.el-input__wrapper) {
                  padding: 3px 6px;
                }

                :deep(.el-input__inner) {
                  font-size: 12px;
                }
              }
            }

            .clear-search-btn {
              font-size: 12px;
              padding: 4px 8px;
            }

            .mode-toggle-compact {
              .mode-switch-compact {
                :deep(.el-switch__label) {
                  font-size: 11px;
                }
              }
            }
          }
        }

        // 集成式搜索控制中心移动端适配
        .integrated-search-center {
          .search-control-panel {
            grid-template-columns: 1fr;
            gap: 16px;
            padding: 20px;
            border-radius: 16px;
            align-items: stretch; // 移动端也保持拉伸对齐

            .control-section {
              min-height: 100px; // 移动端适当减小最小高度

              .section-header {
                height: 28px; // 移动端标题区域高度

                .section-title {
                  font-size: 13px;
                }

                .section-icon {
                  font-size: 16px;
                }
              }

              &.kb-section {
                .kb-select-integrated {
                  :deep(.el-input__wrapper) {
                    padding: 10px 12px;
                    min-height: 44px; // 移动端最小高度
                  }

                  :deep(.el-input__inner) {
                    font-size: 14px;
                  }
                }
              }

              &.mode-section {
                .mode-toggle-integrated {
                  padding: 10px;
                  min-height: 44px; // 与选择器保持一致

                  .mode-switch-integrated {
                    :deep(.el-switch__label) {
                      font-size: 13px;
                    }
                  }
                }
              }
            }
          }

          .mode-description-integrated {
            .mode-desc-card {
              padding: 10px 16px;
              max-width: 100%;

              .desc-icon {
                font-size: 14px;
              }

              .desc-text {
                font-size: 12px;
              }
            }
          }
        }

        .search-input-wrapper {
          .main-search-input {
            :deep(.el-input__wrapper) {
              height: 56px;
              border-radius: 28px;
            }

            :deep(.el-input__inner) {
              font-size: 16px;
              padding: 0 20px;
            }

            :deep(.el-input__suffix) {
              .search-actions {
                .search-button {
                  height: 40px;
                  padding: 0 16px;
                  font-size: 14px;
                }
              }
            }

            // 紧凑模式移动端优化
            &.compact-mode {
              :deep(.el-input__wrapper) {
                height: 44px !important;
                border-radius: 22px !important;
              }

              :deep(.el-input__inner) {
                font-size: 14px !important;
                padding: 0 16px !important;
              }

              :deep(.el-input__suffix) {
                .search-actions {
                  .search-button.compact-mode {
                    height: 32px !important;
                    padding: 0 10px !important;
                    font-size: 12px !important;
                  }
                }
              }
            }
          }
        }
      }
    }

    .results-section {
      padding: 16px;

      // 只在有搜索结果时设置移动端最小高度
      .modern-search-page.has-results & {
        min-height: calc(100vh - 11vh);
      }

      .results-container {
        .results-content {
          .result-item {
            padding: 16px;

            .result-main {
              .result-header {
                .result-title {
                  font-size: 16px;
                }

                .result-meta {
                  gap: 8px;
                }
              }

              .result-footer {
                flex-direction: column;
                align-items: flex-start;
                gap: 12px;
              }
            }
          }
        }
      }
    }
  }
}

// ==================== 搜索结果增强样式 ====================
// 搜索结果项增强样式
.result-title-row {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
  margin-bottom: 8px;

  .result-title {
    flex: 1;
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-color-primary);
    line-height: 1.4;
  }

  .result-score {
    flex-shrink: 0;

    .score-tag {
      font-weight: 600;
      border-radius: 12px;
    }
  }
}

// 文档片段预览样式
.result-chunks-preview {
  margin-top: 16px;
  padding: 12px;
  background: var(--fill-color-extra-light);
  border-radius: 8px;
  border: 1px solid var(--border-color-lighter);

  .chunks-header {
    display: flex;
    align-items: center;
    gap: 6px;
    margin-bottom: 12px;
    font-size: 13px;
    font-weight: 600;
    color: var(--text-color-secondary);

    .el-icon {
      color: var(--warning-color);
    }
  }

  .chunk-item {
    display: flex;
    gap: 8px;
    margin-bottom: 8px;
    padding: 8px;
    background: var(--card-bg);
    border-radius: 6px;
    border: 1px solid var(--border-color-lighter);

    &:last-child {
      margin-bottom: 0;
    }

    .chunk-score {
      flex-shrink: 0;
      align-self: flex-start;
    }

    .chunk-text {
      flex: 1;
      font-size: 13px;
      line-height: 1.5;
      color: var(--text-color-regular);

      // 限制显示行数
      display: -webkit-box;
      -webkit-line-clamp: 3;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }
  }

  .more-chunks {
    text-align: center;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid var(--border-color-lighter);
  }
}

// 搜索统计信息样式
.total-found {
  color: var(--text-color-placeholder);
  font-size: 12px;
  margin-left: 4px;
}

// 搜索高亮样式增强
:deep(.search-highlight) {
  background: linear-gradient(120deg, #ffd700 0%, #ffed4e 100%);
  color: #333;
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 600;
}
</style>

<style lang="scss">
// 知识库选项样式 - 主题适配
.el-select-dropdown {
  border-radius: 12px;
  box-shadow: var(--box-shadow-lg);
  border: 1px solid var(--border-color-lighter);
  padding: 8px;
  background: var(--bg-color) !important; // 主题背景适配

  .el-select-dropdown__item {
    border-radius: 8px;
    margin-bottom: 4px;
    padding: 6px;
    transition: all 0.2s ease;
    color: var(--text-color-primary) !important; // 主题文字色彩
    background: transparent;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: var(--primary-lighter) !important;
      color: var(--primary-color) !important;
    }

    &.is-selected {
      background: var(--primary-color) !important;
      color: white !important;

      .kb-option-content {
        .kb-option-main {
          .kb-icon {
            color: white !important;
          }

          .kb-count {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border-color: transparent !important;
          }
        }

        .kb-description {
          color: rgba(255, 255, 255, 0.8) !important;
        }
      }

      // 集成式选项样式
      .kb-option-integrated {
        .kb-icon {
          color: white !important;
        }

        .kb-name {
          color: white !important;
        }

        .kb-count {
          background: rgba(255, 255, 255, 0.2) !important;
          color: white !important;
          border-color: transparent !important;
        }
      }
    }
  }
}

</style>
