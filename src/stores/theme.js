import { defineStore } from 'pinia'
import { ref, computed, watch } from 'vue'

/**
 * 主题状态管理
 * 支持浅色/深色主题切换，状态持久化到localStorage
 */
export const useThemeStore = defineStore('theme', () => {
  // 主题模式：'light' | 'dark'
  const theme = ref(localStorage.getItem('theme') || 'light')

  // 计算属性：是否为深色主题
  const isDark = computed(() =>{
    console.log(theme.value)
    return theme.value === 'dark'
  })

  // 计算属性：当前主题名称（用于显示）
  const themeName = computed(() => {
    return theme.value === 'dark' ? '深色主题' : '浅色主题'
  })

  // 计算属性：主题图标
  const themeIcon = computed(() => {
    return theme.value === 'dark' ? 'Sunny' : 'Moon'
  })

  /**
   * 切换主题
   */
  const toggleTheme = () => {
    theme.value = theme.value === 'light' ? 'dark' : 'light'
  }

  /**
   * 设置主题
   * @param {string} newTheme - 主题名称 'light' | 'dark'
   */
  const setTheme = (newTheme) => {
    if (['light', 'dark'].includes(newTheme)) {
      theme.value = newTheme
    }
  }

  /**
   * 应用主题到DOM
   */
  const applyTheme = () => {
    const html = document.documentElement

    if (theme.value === 'dark') {
      html.setAttribute('data-theme', 'dark')
      html.classList.add('dark')
    } else {
      html.removeAttribute('data-theme')
      html.classList.remove('dark')
    }
  }

  /**
   * 初始化主题
   * 在应用启动时调用
   */
  const initTheme = () => {
    // 如果localStorage中没有保存的主题，检查系统偏好
    if (!localStorage.getItem('theme')) {
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches
      theme.value = prefersDark ? 'dark' : 'light'
    }

    // 应用主题
    applyTheme()

    // 监听系统主题变化（可选功能）
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)')
    const handleSystemThemeChange = (e) => {
      // 只有在用户没有手动设置主题时才跟随系统
      if (!localStorage.getItem('theme-user-set')) {
        theme.value = e.matches ? 'dark' : 'light'
      }
    }

    mediaQuery.addEventListener('change', handleSystemThemeChange)

    // 返回清理函数
    return () => {
      mediaQuery.removeEventListener('change', handleSystemThemeChange)
    }
  }

  // 监听主题变化，自动保存到localStorage并应用到DOM
  watch(theme, (newTheme) => {
    localStorage.setItem('theme', newTheme)
    localStorage.setItem('theme-user-set', 'true') // 标记用户手动设置
    applyTheme()
  }, { immediate: false })

  return {
    // 状态
    theme,
    isDark,
    themeName,
    themeIcon,

    // 方法
    toggleTheme,
    setTheme,
    applyTheme,
    initTheme
  }
})
