import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { authAPI, /*userAPI*/ } from '@/api'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref(localStorage.getItem('user-token') || '')
  const userInfo = ref(JSON.parse(localStorage.getItem('user-info') || 'null'))
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => !!token.value && !!userInfo.value)
  const userName = computed(() => userInfo.value?.name || '')
  const userAvatar = computed(() => userInfo.value?.avatar || '')
  const userRole = computed(() => userInfo.value?.role || 'user')

  // 模拟用户数据
  const mockUsers = [
    {
      id: 1,
      username: 'admin',
      password: '123456',
      name: '管理员',
      email: '<EMAIL>',
      role: 'admin',
      avatar: 'https://cube.elemecdn.com/0/88/03b0d39583f48206768a7534e55bcpng.png'
    },
    {
      id: 2,
      username: 'user',
      password: '123456',
      name: '普通用户',
      email: '<EMAIL>',
      role: 'user',
      avatar: 'https://cube.elemecdn.com/9/c2/f0ee8a3c7c9638a54940382568c9dpng.png'
    },
    {
      id: 3,
      username: 'demo',
      password: 'demo123',
      name: '演示用户',
      email: '<EMAIL>',
      role: 'user',
      avatar: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png'
    }
  ]

  // 真实登录API（保留模拟数据作为fallback）
  const performLogin = async (credentials) => {
    try {
      // 尝试使用真实API
      const response = await authAPI.login(credentials)

      // 假设后端返回格式为 { token, user, refresh_token }
      return {
        token: response.token || response.access_token,
        userInfo: response.user || response.userInfo,
        refreshToken: response.refresh_token
      }
    } catch (error) {
      // 如果API调用失败，使用模拟数据作为fallback
      console.warn('API登录失败，使用模拟数据:', error.message)

      return new Promise((resolve, reject) => {
        setTimeout(() => {
          const user = mockUsers.find(
            u => u.username === credentials.username && u.password === credentials.password
          )

          if (user) {
            const token = `mock-token-${user.id}-${Date.now()}`
            const userInfo = {
              id: user.id,
              name: user.name,
              username: user.username,
              email: user.email,
              role: user.role,
              avatar: user.avatar
            }
            resolve({ token, userInfo })
          } else {
            reject(new Error('用户名或密码错误'))
          }
        }, 1000)
      })
    }
  }

  // 登录方法
  const login = async (credentials) => {
    try {
      isLoading.value = true
      const response = await performLogin(credentials)

      // 保存token和用户信息
      token.value = response.token
      userInfo.value = response.userInfo

      // 持久化存储
      localStorage.setItem('user-token', response.token)
      localStorage.setItem('user-info', JSON.stringify(response.userInfo))

      // 保存刷新token（如果有）
      if (response.refreshToken) {
        localStorage.setItem('refresh-token', response.refreshToken)
      }

      ElMessage.success(`欢迎回来，${response.userInfo.name}！`)
      return response
    } catch (error) {
      ElMessage.error(error.message || '登录失败')
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // 登出方法
  const logout = async () => {
    try {
      // 尝试调用后端登出API
      if (token.value) {
        try {
          await authAPI.logout()
        } catch (error) {
          console.warn('后端登出失败，继续本地登出:', error.message)
        }
      }
    } catch (error) {
      console.error('登出过程中出现错误:', error)
    } finally {
      // 无论后端调用是否成功，都要清除本地状态
      token.value = ''
      userInfo.value = null

      // 清除本地存储
      localStorage.removeItem('user-token')
      localStorage.removeItem('user-info')
      localStorage.removeItem('refresh-token')

      ElMessage.success('已安全退出')
    }
  }

  // 自动登录检查
  const checkAutoLogin = () => {
    const savedToken = localStorage.getItem('user-token')
    const savedUserInfo = localStorage.getItem('user-info')

    if (savedToken && savedUserInfo) {
      try {
        token.value = savedToken
        userInfo.value = JSON.parse(savedUserInfo)
        return true
      } catch (error) {
        console.error('自动登录失败:', error)
        logout().then(r => {
          console.log('登出成功:', r)
        })
        return false
      }
    }
    return false
  }

  // 更新用户信息
  // const updateUserInfo = async (newInfo) => {
  //   try {
  //     // 如果有新信息需要同步到后端
  //     if (Object.keys(newInfo).length > 0) {
  //       try {
  //         const updatedUser = await userAPI.updateUserInfo(newInfo)
  //         // 使用后端返回的最新信息
  //         userInfo.value = { ...userInfo.value, ...updatedUser }
  //       } catch (error) {
  //         console.warn('后端更新用户信息失败，仅更新本地:', error.message)
  //         // 如果后端更新失败，仍然更新本地信息
  //         userInfo.value = { ...userInfo.value, ...newInfo }
  //       }
  //     } else {
  //       userInfo.value = { ...userInfo.value, ...newInfo }
  //     }
  //
  //     localStorage.setItem('user-info', JSON.stringify(userInfo.value))
  //     return userInfo.value
  //   } catch (error) {
  //     console.error('更新用户信息失败:', error)
  //     throw error
  //   }
  // }

  // 刷新用户信息
  // const refreshUserInfo = async () => {
  //   try {
  //     const latestUserInfo = await userAPI.getUserInfo()
  //     userInfo.value = latestUserInfo
  //     localStorage.setItem('user-info', JSON.stringify(latestUserInfo))
  //     return latestUserInfo
  //   } catch (error) {
  //     console.warn('刷新用户信息失败:', error.message)
  //     return userInfo.value
  //   }
  // }

  // 重置状态
  const resetState = () => {
    token.value = ''
    userInfo.value = null
    isLoading.value = false
  }

  return {
    // 状态
    token,
    userInfo,
    isLoading,

    // 计算属性
    isLoggedIn,
    userName,
    userAvatar,
    userRole,

    // 方法
    login,
    logout,
    checkAutoLogin,
    resetState,

    // 模拟数据（开发时使用）
    mockUsers
  }
})
