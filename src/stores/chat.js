import {defineStore} from 'pinia'
import {ref, computed} from 'vue'
import { v4 as uuidv4 } from 'uuid'
/**
 * AI对话状态管理
 */
export const useChatStore = defineStore('chat', () => {
    // ==================== 状态定义 ====================

    // 对话历史
    const messages = ref([])

    // 当前对话模式：'chat' | 'report'
    const currentMode = ref('chat')

    // 选中的知识库
    const selectedKnowledgeBase = ref('')

    // 当前是否正在对话中
    const isLoading = ref(false)

    // 当前SSE连接
    const currentSSEConnection = ref(null)

    // 当前流式响应的消息ID
    const streamingMessageId = ref(null)

    // 当前对话状态信息
    const currentStatus = ref('')

    // 当前进度
    const currentProgress = ref(0)

    // ==================== 计算属性 ====================

    // 获取对话模式显示名称
    const modeDisplayName = computed(() => {
        return currentMode.value === 'chat' ? '普通对话' : '编报模式'
    })

    // 是否有对话历史
    const hasMessages = computed(() => {
        return messages.value.length > 0
    })

    // 获取最后一条消息
    const lastMessage = computed(() => {
        return messages.value.length > 0 ? messages.value[messages.value.length - 1] : null
    })

    // ==================== 方法定义 ====================

    /**
     * 添加用户消息
     * @param {string} content 消息内容
     * @param {string} knowledgeBase 知识库名称
     */
    const addUserMessage = (content, knowledgeBase = '') => {
        const message = {
            id: generateMessageId(),
            type: 'user',
            content,
            timestamp: new Date(),
            knowledgeBase,
            mode: currentMode.value
        }
        messages.value.push(message)
        return message.id
    }

    /**
     * 添加AI消息
     * @param {string} content 消息内容（可为空，用于流式更新）
     * @param {Object} metadata 消息元数据
     */
    const addAIMessage = (content = '', metadata = {}) => {
        const message = {
            id: generateMessageId(),
            type: 'ai',
            content,
            timestamp: new Date(),
            mode: currentMode.value,
            status: 'generating', // generating | completed | error
            progress: 0,
            statusText: '',
            sources: [],
            confidence: 0,
            reasoning: '', ...metadata
        }
        messages.value.push(message)
        return message.id
    }

    /**
     * 更新AI消息内容（用于流式响应）
     * @param {string} messageId 消息ID
     * @param {Object} updates 更新内容
     */
    const updateAIMessage = (messageId, updates) => {
        const messageIndex = messages.value.findIndex(msg => msg.id === messageId)
        if (messageIndex !== -1) {
            messages.value[messageIndex] = {
                ...messages.value[messageIndex], ...updates, timestamp: new Date()
            }
        }
    }

    /**
     * 处理SSE消息更新
     * @param {string} messageId 消息ID
     * @param {string} eventType 事件类型
     * @param {Object} data 事件数据
     */
    const handleSSEMessage = (messageId, eventType, data) => {
        const currentMessage = messages.value.find(msg => msg.id === messageId)
        const existingThoughtChain = currentMessage?.thoughtChain || []

        const updates = {
            thoughtChain: [...existingThoughtChain] // 复制现有的thoughtChain
        }
        switch (eventType) {
            case 'thinking':
                updates.statusText = data.status || '正在思考...'
                updates.progress = data.progress || 0
                if (updates.thoughtChain.length > 0) {
                    updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                }
                updates.thoughtChain.push({
                    id: uuidv4(),
                    title: data.status,
                    thinkContent: data.content,
                    status: 'loading',
                    isDefaultExpand: false,
                    isCanExpand: true
                })
                break

            case 'assessment':
                updates.statusText = data.status || '评估完成'
                updates.progress = data.progress || 25
                updates.confidence = data.confidence
                updates.reasoning = data.reasoning
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id: uuidv4(),
                    title: data.status,
                    thinkContent: data.reasoning,
                    status: 'loading',
                    isDefaultExpand: false,
                    isCanExpand: true
                })
                break

            case 'searching':
                updates.statusText = data.status || '正在搜索...'
                updates.progress = data.progress || 40
                // console.log(updates.thoughtChain[updates.thoughtChain.length - 1].status)
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id: uuidv4(),
                    isDefaultExpand: false,
                    isCanExpand: true,
                    title: data.status,
                    isMarkdown: true,
                    thinkContent: data.queries?.join(', ') || `

**策略组**
${data.strategy_group}
**任务总数**
${data.tasks_in_group}
            `,
                    status: 'loading'
                })
                break

            case 'found_sources':
                updates.statusText = data.status || '找到相关资源'
                updates.progress = data.progress || 60
                updates.sources = data.sources || []
                // console.log(updates.thoughtChain[updates.thoughtChain.length - 1].status)
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id: uuidv4(),
                    isDefaultExpand: false,
                    isCanExpand: true,
                    title: data.status || data.query,
                    isMarkdown: true,
                    thinkContent: data.sources?.map(source => `${source.file_name}`).join('\n') ||
                        `
**搜索类型**
${data.search_type}
**搜索到的文件**
${data.documents.map(doc => `
文件名: ${doc.file_name}
摘要: ${doc.summary}
`)}
`,
                    status: 'loading'
                })
                break

            case 'generating':
                updates.statusText = data.status || '正在生成回答...'
                updates.progress = data.progress || 75
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id: uuidv4(),
                    isDefaultExpand: false,
                    isCanExpand: false,
                    title:  data.status || '正在生成回答...',
                    status: 'loading'
                })
                break

            case 'streaming':
                // 流式更新内容
                if (data.content) {
                    const currentMessage = messages.value.find(msg => msg.id === messageId)
                    if (currentMessage) {
                        // 使用full_content优先，如果没有则累加content
                        updates.content = data.full_content || (currentMessage.content + data.content)
                    }
                }
                updates.progress = data.progress || 80
                break

            case 'completed':
                updates.status = 'completed'
                updates.statusText = '回答完成'
                updates.progress = 100
                // 支持普通对话模式(answer字段)和编报模式(report字段)
                updates.content = data.answer || data.report || updates.content
                updates.confidence = data.confidence_score
                updates.sources = data.sources || []
                updates.reasoning = data.reasoning_process
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                break

            // 编报模式特有事件
            case 'planning':
                updates.statusText = data.status || '制定研究计划...'
                updates.progress = data.progress || 15
                updates.plan = data.plan
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id: uuidv4(),
                    isDefaultExpand: false,
                    isCanExpand: true,
                    title: data.status,
                    isMarkdown: true,
                    thinkContent: `
# ${data.plan.main_topic}
**目标**
${data.plan.objectives.map(obj => `- ${obj}`).join('\n')}
**预期部分**
${data.plan.expected_sections.map(section => `- ${section}`).join('\n')}
**研究范围**
${data.plan.research_scope}
                    `,
                    status: 'loading'
                })
                break

            case 'analyzing':
                updates.statusText = data.status || '分析内容...'
                updates.progress = data.progress || 65
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id: uuidv4(),
                    isDefaultExpand: false,
                    isCanExpand: false,
                    title: data.status,
                    status: 'loading',

                })
                break

            case 'reflecting':
                updates.statusText = data.status || '评估内容完整性...'
                updates.progress = data.progress || 75
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id: uuidv4(),
                    isDefaultExpand: false,
                    isCanExpand: true,
                    title: data.status,
                    isMarkdown: true,
                    status: 'loading',
                    thinkContent: `
**缺少主题**
${data.analysis.missing_topics.map(topic => `- ${topic}`).join('\n')}
**质量评估**
${data.analysis.quality_assessment}
                    `
                })
                break

            case 'supplementing':
                updates.statusText = data.status || '补充搜索...'
                updates.progress = data.progress || 55
                updates.thoughtChain[updates.thoughtChain.length - 1].status = 'success'
                updates.thoughtChain.push({
                    id:uuidv4(),
                    isDefaultExpand: false,
                    isCanExpand: true,
                    title: data.status,
                    isMarkdown: true,
                    status: 'loading',
                    tinkContent: `
**补充搜索**
${data.reason}
                    `
                })
                break
        }
        updateAIMessage(messageId, updates)
    }

    /**
     * 设置对话模式
     * @param {string} mode 对话模式
     */
    const setMode = (mode) => {
        currentMode.value = mode
    }

    /**
     * 设置知识库
     * @param {string} knowledgeBase 知识库名称
     */
    const setKnowledgeBase = (knowledgeBase) => {
        selectedKnowledgeBase.value = knowledgeBase
    }

    /**
     * 设置加载状态
     * @param {boolean} loading 是否加载中
     */
    const setLoading = (loading) => {
        isLoading.value = loading
    }

    /**
     * 设置SSE连接
     * @param {Object} connection SSE连接对象
     */
    const setSSEConnection = (connection) => {
        currentSSEConnection.value = connection
    }

    /**
     * 设置流式消息ID
     * @param {string} messageId 消息ID
     */
    const setStreamingMessageId = (messageId) => {
        streamingMessageId.value = messageId
    }

    /**
     * 清空对话历史
     */
    const clearMessages = () => {
        messages.value = []
    }

    /**
     * 停止当前对话
     */
    const stopCurrentChat = () => {
        if (currentSSEConnection.value) {
            currentSSEConnection.value.close()
            currentSSEConnection.value = null
        }

        // 更新最后一条AI消息状态
        if (streamingMessageId.value) {
            updateAIMessage(streamingMessageId.value, {
                status: 'stopped', statusText: '对话已停止', progress: 0
            })
            streamingMessageId.value = null
        }

        setLoading(false)
        currentStatus.value = ''
        currentProgress.value = 0
    }

    /**
     * 生成消息ID
     */
    const generateMessageId = () => {
        return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
    }

    // ==================== 返回状态和方法 ====================
    return {
        // 状态
        messages,
        currentMode,
        selectedKnowledgeBase,
        isLoading,
        currentSSEConnection,
        streamingMessageId,
        currentStatus,
        currentProgress,

        // 计算属性
        modeDisplayName,
        hasMessages,
        lastMessage,

        // 方法
        addUserMessage,
        addAIMessage,
        updateAIMessage,
        handleSSEMessage,
        setMode,
        setKnowledgeBase,
        setLoading,
        setSSEConnection,
        setStreamingMessageId,
        clearMessages,
        stopCurrentChat
    }
})
