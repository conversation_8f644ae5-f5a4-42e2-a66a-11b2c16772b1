<template>
  <el-header class="app-header">
    <div class="header-left">
      <div class="logo">
        <h1>知识库系统</h1>
      </div>
    </div>

    <div class="header-center">
      <el-menu
        mode="horizontal"
        :default-active="activeIndex"
        class="header-menu"
        router
        @select="handleSelect"
      >
        <el-menu-item index="/">首页</el-menu-item>
        <el-menu-item index="/knowledge">知识管理</el-menu-item>
        <el-menu-item index="/search">搜索</el-menu-item>
      </el-menu>
    </div>

    <div class="header-right">
      <!-- 主题切换按钮 -->
      <el-tooltip
        :content="`切换到${themeStore.isDark ? '浅色' : '深色'}主题`"
        placement="bottom"
      >
        <button
          class="theme-toggle-btn"
          @click="handleThemeToggle"
          :aria-label="`切换到${themeStore.isDark ? '浅色' : '深色'}主题`"
        >
          <el-icon>
            <Sunny v-if="themeStore.isDark" />
            <Moon v-else />
          </el-icon>
        </button>
      </el-tooltip>

      <!-- 用户菜单 -->
      <div v-if="userStore.isLoggedIn" class="user-section">
        <el-dropdown trigger="click" class="user-dropdown">
          <span class="user-info">
            <el-avatar :size="32" :src="userStore.userAvatar" />
            <span class="username">{{ userStore.userName }}</span>
            <el-icon class="el-icon--right">
              <ArrowDown />
            </el-icon>
          </span>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item @click="goToProfile">
                <el-icon><User /></el-icon>
                个人中心
              </el-dropdown-item>
              <el-dropdown-item @click="goToSettings">
                <el-icon><Setting /></el-icon>
                账户设置
              </el-dropdown-item>
              <el-dropdown-item divided @click="handleLogout">
                <el-icon><SwitchButton /></el-icon>
                退出登录
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>

      <!-- 登录按钮 -->
      <div v-else class="login-section">
        <el-button type="primary" @click="goToLogin">
          <el-icon><User /></el-icon>
          登录
        </el-button>
      </div>
    </div>
  </el-header>
</template>

<script setup>
import { ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import {
  Sunny,
  Moon,
  ArrowDown,
  User,
  Setting,
  SwitchButton
} from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'

// 路由和状态管理
const route = useRoute()
const router = useRouter()
const themeStore = useThemeStore()
const userStore = useUserStore()

// 根据当前路由设置活跃菜单项
const activeIndex = computed(() => {
  return route.path
})

/**
 * 处理菜单选择
 */
const handleSelect = (key, keyPath) => {
  console.log('菜单选择:', key, keyPath)
  // Element Plus的router模式会自动处理路由跳转
  // 这里可以添加额外的逻辑，比如埋点统计等
}

/**
 * 处理主题切换
 */
const handleThemeToggle = () => {
  themeStore.toggleTheme()

  // 显示切换提示
  ElMessage({
    message: `已切换到${themeStore.themeName}`,
    type: 'success',
    duration: 2000,
    showClose: false
  })
}

/**
 * 跳转到登录页
 */
const goToLogin = () => {
  router.push('/login')
}

/**
 * 跳转到个人中心
 */
const goToProfile = () => {
  ElMessage.info('个人中心功能开发中...')
}

/**
 * 跳转到设置页面
 */
const goToSettings = () => {
  router.push('/settings')
}

/**
 * 处理登出
 */
const handleLogout = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要退出登录吗？',
      '确认退出',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await userStore.logout()
    await router.push('/login')
  } catch {
    ElMessage.info('已取消退出')
  }
}
</script>

<style lang="scss" scoped>
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  height: 60px;
  background-color: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  backdrop-filter: blur(8px);
  position: sticky;
  top: 0;
  z-index: 1000;

  .header-left {
    display: flex;
    align-items: center;

    .logo {
      h1 {
        margin: 0;
        font-size: 20px;
        font-weight: 600;
        color: var(--header-text);
        background: linear-gradient(135deg, var(--primary-color), var(--info-color));
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
      }
    }
  }

  .header-center {
    flex: 1;
    display: flex;
    justify-content: center;

    .header-menu {
      background-color: transparent;
      border-bottom: none;
      width: 100%;
      display: flex;
      justify-content: center; // 菜单项左右居中

      :deep(.el-menu-item) {
        color: var(--header-text);
        background-color: transparent;
        border-bottom: 2px solid transparent;
        border-radius: 8px 8px 0 0;
        margin: 0 4px;
        transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        position: relative;
        overflow: hidden;

        // 添加渐变背景效果
        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: var(--primary-gradient);
          opacity: 0;
          transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
          z-index: -1;
        }

        &:hover {
          color: var(--primary-color);
          background-color: var(--header-hover);
          transform: translateY(-1px);

          &::before {
            opacity: 0.05;
          }
        }

        &.is-active {
          color: var(--primary-color);
          background-color: var(--primary-light);
          border-bottom-color: var(--primary-color);
          font-weight: 600;

          &::before {
            opacity: 0.1;
          }

          // 添加选中状态的特殊效果
          &::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 20px;
            height: 2px;
            background: var(--primary-color);
            border-radius: 1px;
            box-shadow: 0 0 8px var(--primary-color);
          }
        }

        // 文字内容
        .el-menu-item__title {
          color: inherit;
          transition: color 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        }
      }
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;

    .theme-toggle-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 40px;
      height: 40px;
      border: none;
      border-radius: 10px;
      background: var(--fill-color-light);
      color: var(--header-text);
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: var(--primary-gradient);
        opacity: 0;
        transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
        z-index: -1;
      }

      &:hover {
        background: var(--header-hover);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: var(--box-shadow-sm);

        &::before {
          opacity: 0.1;
        }
      }

      &:active {
        transform: translateY(0);
      }

      .el-icon {
        font-size: 18px;
        transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
      }

      &:hover .el-icon {
        transform: rotate(15deg) scale(1.1);
      }
    }

    .user-dropdown {
      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        padding: 8px 12px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        color: var(--header-text);

        &:hover {
          background-color: var(--header-hover);
        }

        .username {
          font-size: 14px;
          font-weight: 500;
        }

        .el-icon {
          font-size: 12px;
          transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }

        &:hover .el-icon {
          transform: rotate(180deg);
        }
      }
    }

    .login-section {
      .el-button {
        height: 36px;
        padding: 0 16px;
        border-radius: 8px;
        font-weight: 500;

        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .app-header {
    padding: 0 16px;

    .header-center {
      display: none;
    }

    .header-left .logo h1 {
      font-size: 18px;
    }

    .header-right {
      gap: 12px;

      .user-dropdown .user-info .username {
        display: none;
      }
    }
  }
}

// 深色主题特殊处理
:deep([data-theme="dark"]) {
  .app-header {
    .header-menu {
      .el-menu-item {
        &.is-active {
          background-color: var(--primary-light);
          color: var(--primary-color);

          &::before {
            opacity: 0.15;
          }

          &::after {
            box-shadow: 0 0 12px var(--primary-color);
          }
        }

        &:hover {
          background-color: var(--header-hover);

          &::before {
            opacity: 0.08;
          }
        }
      }
    }

    .theme-toggle-btn {
      background: var(--fill-color-dark);

      &:hover {
        background: var(--header-hover);

        &::before {
          opacity: 0.15;
        }
      }
    }

    .user-dropdown .user-info {
      &:hover {
        background-color: var(--header-hover);
      }
    }
  }
}

// Element Plus 菜单组件全局样式覆盖
:deep(.el-menu--horizontal) {
  background-color: transparent !important;
  border-bottom: none !important;

  .el-menu-item {
    background-color: transparent !important;

    &:hover {
      background-color: var(--header-hover) !important;
    }

    &.is-active {
      background-color: var(--primary-light) !important;
      color: var(--primary-color) !important;
    }
  }
}

// 确保主题切换时的平滑过渡
.app-header * {
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
</style>
