<template>
  <div class="chat-message" :class="messageClass">
    <!-- 用户消息 -->
    <div v-if="message.type === 'user'" class="user-message">
      <div class="message-content">
        <div class="user-content">{{ message.content }}</div>
        <div class="mode-tag">
          <el-tag size="small" :type="message.mode === 'chat' ? 'primary' : 'warning'">
            {{ message.mode === 'chat' ? '普通对话' : '编报模式' }}
          </el-tag>
          <el-tag v-if="message.knowledgeBase" size="small" type="info">{{ message.knowledgeBase }}</el-tag>
        </div>
      </div>
      <div class="message-header">
        <div class="message-info">
          <span class="sender-name">您</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>
        <el-avatar :size="32" class="user-avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>
    </div>

    <!-- AI消息 -->
    <div v-else class="ai-message">
      <div class="message-header">
        <el-avatar :size="32" class="ai-avatar">
          <el-icon><Box /></el-icon>
        </el-avatar>
        <div class="message-info">
          <span class="sender-name">AI助手</span>
          <span class="message-time">{{ formatTime(message.timestamp) }}</span>
        </div>
      </div>

      <div  class="message-content">
        <!-- 推理过程 - 独立显示，不受消息状态限制 -->
        <div class="reasoning">
<!--          <div class="reasoning-header">-->
<!--            <el-icon><Document /></el-icon>-->
<!--            <span>推理过程</span>-->
<!--          </div>-->
          <div class="thought-chain-container">
            <ThoughtChain
                :thinking-items="message.thoughtChain"
                :show-timeline="true"
                :collapsible="true"
                class="thought-chain-component"
            />
          </div>
        </div>
        <!-- 思考过程 - 只在生成中且有思考内容时显示 -->
        <div v-if="thinkingContent" class="thinking-section">
          <Thinking
              :content="thinkingContent"
              :is-thinking="true"
              auto-collapse
              :status="think_status"
              class="thinking-component"
          />
        </div>


        <!-- 状态指示器 - 在生成过程中显示 -->
        <div v-if="message.status === 'generating'" class="status-indicator">
          <!-- 进度条和状态文本 -->
          <div class="progress-section">
            <el-progress
              :percentage="message.progress || 0"
              :show-text="false"
              :stroke-width="3"
              class="progress-bar"
            />
            <div class="status-text">
              <el-icon class="loading-icon"><Loading /></el-icon>
              {{ message.statusText || '正在处理...' }}
            </div>
          </div>
        </div>

        <!-- AI回复内容 -->
        <div v-if="displayContent" class="ai-content">
          <!-- 使用Element-Plus-X的打字机组件 -->
          <Typewriter
            :content="displayContent"
            :is-streaming="true"
            :is-markdown="true"
            :speed="50"
            class="streaming-content"
          />
        </div>

        <!-- 消息元数据 - 只在完成状态显示 -->
        <div v-if="message.status === 'completed'" class="message-metadata">
          <!-- 置信度 -->
          <div v-if="message.confidence" class="confidence-score">
            <span class="label">置信度：</span>
            <el-rate
              v-model="confidenceStars"
              :max="5"
              disabled
              show-score
              text-color="#ff9900"
              score-template="{value}%"
            />
          </div>

          <!-- 知识来源 -->
          <div v-if="message.sources && message.sources.length > 0" class="sources">
            <div class="sources-header">
              <el-icon><Document /></el-icon>
              <span>参考资料 ({{ message.sources.length }})</span>
            </div>
            <div class="sources-list">
              <el-collapse accordion>
                <el-collapse-item
                  v-for="(source, index) in message.sources"
                  :key="index"
                  :title="source.file_name"
                  :name="index"
                >
                  <div class="source-content">
                    <div class="source-text">{{ source.chunk_text }}</div>
                    <div class="source-score">
                      相关度: {{ (source.relevance_score * 100).toFixed(1) }}%
                    </div>
                  </div>
                </el-collapse-item>
              </el-collapse>
            </div>
          </div>
        </div>

        <!-- 错误状态 -->
        <div v-if="message.status === 'error'" class="error-message">
          <el-alert
            title="回答生成失败"
            type="error"
            :description="message.error || '未知错误'"
            show-icon
            :closable="false"
          />
        </div>

        <!-- 停止状态 -->
        <div v-if="message.status === 'stopped'" class="stopped-message">
          <el-alert
            title="对话已停止"
            type="warning"
            description="用户主动停止了对话生成"
            show-icon
            :closable="false"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { User, Box, Loading, Document } from '@element-plus/icons-vue'
import { Typewriter, ThoughtChain, Thinking } from 'vue-element-plus-x'

// 组件属性
const props = defineProps({
  message: {
    type: Object,
    required: true
  }
})

// 计算属性
const messageClass = computed(() => {
  return {
    'user-message-wrapper': props.message.type === 'user',
    'ai-message-wrapper': props.message.type === 'ai',
    'generating': props.message.status === 'generating',
    'completed': props.message.status === 'completed',
    'error': props.message.status === 'error'
  }
})

const confidenceStars = computed(() => {
  if (!props.message.confidence) return 0
  return Math.round((props.message.confidence * 5))
})
let isThinking = false
const reasoning_content = ref('')
const think_status = ref( '')
// 处理思考内容，提取<think>标签中的内容
const thinkingContent = computed(() => {
  if (!props.message.content) return ''
  const thinkStart = props.message.content.includes('<think>');
  const thinkEnd = props.message.content.includes('</think>');
  if (thinkStart) {
    isThinking = true;
    think_status.value = "thinking"
  }
  if (thinkEnd) {
    isThinking = false
    think_status.value = "end"
  }
  // 提取<think>标签中的内容
  if (isThinking) {
    reasoning_content.value = props.message.content
        .replace('<think>', '')
        .replace('</think>', '');

  }
  return reasoning_content.value
  // return ''
})

// 处理推理过程数据，转换为ThoughtChain组件所需的格式
const thoughtChainData = computed(() => {
  if (!props.message.reasoning) return []

  try {
    // 如果reasoning是字符串，尝试解析为JSON
    let reasoningData = props.message.reasoning
    if (typeof reasoningData === 'string') {
      // 尝试解析JSON格式的推理过程
      try {
        reasoningData = JSON.parse(reasoningData)
      } catch {
        // 如果不是JSON，按段落分割
        const paragraphs = reasoningData.split('\n').filter(p => p.trim())
        return paragraphs.map((paragraph, index) => ({
          id: `step-${index + 1}`,
          title: `推理步骤 ${index + 1}`,
          content: paragraph.trim(),
          status: 'completed',
          timestamp: new Date(Date.now() - (paragraphs.length - index) * 1000).toISOString()
        }))
      }
    }

    // 如果是对象或数组，转换为ThoughtChain格式
    if (Array.isArray(reasoningData)) {
      return reasoningData.map((item, index) => ({
        id: item.id || `step-${index + 1}`,
        title: item.title || item.step || `推理步骤 ${index + 1}`,
        content: item.content || item.description || item.reasoning || String(item),
        status: item.status || 'completed',
        timestamp: item.timestamp || new Date(Date.now() - (reasoningData.length - index) * 1000).toISOString()
      }))
    } else if (typeof reasoningData === 'object') {
      // 如果是对象，转换为单个步骤
      return [{
        id: 'reasoning-1',
        title: reasoningData.title || '推理过程',
        content: reasoningData.content || reasoningData.reasoning || JSON.stringify(reasoningData, null, 2),
        status: 'completed',
        timestamp: reasoningData.timestamp || new Date().toISOString()
      }]
    }

    return []
  } catch (error) {
    console.error('解析推理过程数据失败:', error)
    return []
  }
})

// 获取显示的内容（排除思考标签）
const displayContent = computed(() => {
  if (!props.message.content || isThinking) return ''
  // 移除<think>标签及其内容，只显示实际回答
  return props.message.content.replace(/<think>[\s\S]*?<\/think>/gi, '').trim()
})
const thoughtChain = computed(()=>{
  return props.message.thoughtChain || []
})
// 方法
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now - date

  if (diff < 60000) { // 1分钟内
    return '刚刚'
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`
  } else if (diff < 86400000) { // 24小时内
    return `${Math.floor(diff / 3600000)}小时前`
  } else {
    return date.toLocaleString('zh-CN', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }
}
</script>

<style lang="scss" scoped>
.chat-message {
  margin-bottom: 24px;

  &.user-message-wrapper {
    display: flex;
    justify-content: flex-end;

    .user-message {
      max-width: 70%;
      width: auto;
    }
  }

  &.ai-message-wrapper {
    .ai-message {
      margin-right: auto;
      max-width: 85%;
    }
  }
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;

  .message-info {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .sender-name {
      font-size: 14px;
      font-weight: 500;
      color: var(--text-color-primary);
    }

    .message-time {
      font-size: 12px;
      color: var(--text-color-secondary);
    }
  }
}

.user-message {
  margin-right: 10px;
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: flex-end;
  gap: 12px;

  .user-avatar {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    flex-shrink: 0;
    border: 2px solid var(--primary-light);
  }

  .message-content {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: #ffffff;
    padding: 12px 16px;
    border-radius: 18px 18px 4px 18px;
    position: relative;
    box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.25);
    max-width: calc(100% - 60px);
    border: 1px solid var(--primary-light);

    .user-content {
      line-height: 1.5;
      word-break: break-word;
    }

    .mode-tag {
      margin-top: 8px;
      display: flex;
      gap: 8px;
      justify-content: flex-end;

      .el-tag {
        font-size: 11px;
      }
    }
  }

  .message-header {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-shrink: 0;

    .message-info {
      display: flex;
      flex-direction: column;
      gap: 2px;
      text-align: right;
    }
  }
}

.ai-message {
  .ai-avatar {
    background: var(--success-color);
  }

  .message-content {
    background: var(--bg-color-page);
    border: 1px solid var(--border-color-light);
    padding: 16px;
    border-radius: 18px 18px 18px 4px;
    box-shadow: var(--box-shadow-sm);

    .thinking-section {
      margin-bottom: 16px;
      // Element-Plus-X Thinking组件主题适配（基于实际DOM结构）
      :deep(.el-thinking) {
        background: transparent;
        border: 1px solid var(--border-color-lighter);
        border-radius: 8px;
        padding: 0;
        overflow: hidden;

        // 触发按钮
        .trigger {
          background: var(--fill-color-lighter);
          border: none;
          padding: 12px 16px;
          width: 100%;
          display: flex;
          align-items: center;
          justify-content: space-between;
          cursor: pointer;
          transition: all 0.3s ease;
          color: var(--text-color-primary);

          &:hover {
            background: var(--fill-color-light);
          }

          &.disabled {
            cursor: default;
          }

          // 状态图标
          .status-icon {
            display: flex;
            align-items: center;
            margin-right: 8px;

            .el-icon {
              color: var(--primary-color);
              font-size: 16px;

              &.end-color {
                color: var(--success-color);
              }
            }
          }

          // 标签文本
          .label {
            flex: 1;
            text-align: left;
            font-size: 14px;
            font-weight: 500;
            color: var(--text-color-primary);
          }

          // 箭头图标
          .arrow {
            color: var(--text-color-secondary);
            font-size: 14px;
            transition: transform 0.3s ease;

            &.expanded {
              transform: rotate(180deg);
            }
          }
        }

        // 内容包装器
        .content-wrapper {
          background: var(--bg-color);
          border-top: 1px solid var(--border-color-lighter);

          // 内容区域
          .content {
            padding: 12px 16px;
            color: var(--text-color-regular);
            line-height: 1.5;
            font-size: 13px;

            pre {
              margin: 0;
              font-family: inherit;
              white-space: pre-wrap;
              word-wrap: break-word;
              color: var(--text-color-regular);
              background: transparent;
              max-width: 66vw;
            }
          }
        }
      }
      .thinking-component {
        border-radius: 8px;
        background: var(--fill-color-lighter);
        border: 1px solid var(--border-color-lighter);
        padding: 12px;
        box-shadow: var(--box-shadow-sm);




      }
    }

    .status-indicator {
      margin-bottom: 12px;

      .progress-section {
        .progress-bar {
          margin-bottom: 8px;
        }

        .status-text {
          display: flex;
          align-items: center;
          gap: 6px;
          font-size: 14px;
          color: var(--text-color-secondary);

          .loading-icon {
            animation: rotate 1s linear infinite;
          }
        }
      }
    }

    .ai-content {
      .streaming-content {
        line-height: 1.6;
        font-size: 14px;
        color: var(--text-color-primary);

        // Element-Plus-X Typewriter组件主题适配
        :deep(.epx-typewriter) {
          color: var(--text-color-primary);

          .epx-typewriter-cursor {
            background: var(--primary-color);
            animation: blink 1s infinite;
          }

          .epx-typewriter-text {
            color: inherit;
          }
        }

        // 更多Typewriter组件样式适配
        :deep(.typewriter-container) {
          .typewriter-line {
            color: var(--text-color-primary);

            &.typing {
              border-right: 2px solid var(--primary-color);
            }
          }

          .typewriter-cursor {
            background: var(--primary-color);
            animation: blink 1s infinite;
          }
        }
      }
    }

    .message-metadata {
      margin-top: 16px;
      padding-top: 16px;
      border-top: 1px solid var(--border-color-lighter);

      .confidence-score {
        display: flex;
        align-items: center;
        gap: 8px;
        margin-bottom: 12px;

        .label {
          font-size: 13px;
          color: var(--text-color-secondary);
        }
      }

      .sources {
        margin-bottom: 16px;
        background: var(--fill-color-lighter);
        border: 1px solid var(--border-color-lighter);
        border-radius: 8px;
        padding: 12px;
        box-shadow: var(--box-shadow-sm);

        .sources-header {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
          color: var(--text-color-primary);
          margin-bottom: 12px;
          font-weight: 500;
          padding-bottom: 8px;
          border-bottom: 1px solid var(--border-color-lighter);

          .el-icon {
            color: var(--primary-color);
            font-size: 16px;
          }
        }

        .sources-list {
          // Element Plus Collapse 组件主题适配
          :deep(.el-collapse) {
            background: transparent;
            border: none;

            .el-collapse-item {
              margin-bottom: 8px;
              background: var(--bg-color);
              border: 1px solid var(--border-color-lighter);
              border-radius: 6px;
              overflow: hidden;
              transition: all 0.3s ease;

              &:last-child {
                margin-bottom: 0;
              }

              &:hover {
                border-color: var(--border-color-light);
                box-shadow: var(--box-shadow-sm);
              }

              &.is-active {
                border-color: var(--primary-light);
                box-shadow: 0 2px 8px rgba(99, 102, 241, 0.1);
              }

              .el-collapse-item__header {
                background: var(--fill-color-lighter);
                color: var(--text-color-primary);
                font-weight: 500;
                font-size: 13px;
                padding: 12px 16px;
                border-bottom: none;
                transition: all 0.3s ease;

                &:hover {
                  background: var(--fill-color-light);
                  color: var(--primary-color);
                }

                .el-collapse-item__arrow {
                  color: var(--text-color-secondary);
                  font-size: 12px;
                  transition: all 0.3s ease;

                  &.is-active {
                    color: var(--primary-color);
                  }
                }
              }

              .el-collapse-item__wrap {
                background: var(--bg-color);
                border-top: 1px solid var(--border-color-lighter);

                .el-collapse-item__content {
                  padding: 16px;
                  background: var(--bg-color);
                }
              }
            }
          }
        }

        .source-content {
          .source-text {
            font-size: 13px;
            line-height: 1.6;
            margin-bottom: 12px;
            color: var(--text-color-regular);
            background: var(--fill-color-lighter);
            padding: 8px 12px;
            border-radius: 4px;
            border-left: 3px solid var(--primary-light);
          }

          .source-score {
            display: inline-flex;
            align-items: center;
            gap: 4px;
            font-size: 12px;
            color: var(--text-color-secondary);
            background: var(--fill-color-light);
            padding: 4px 8px;
            border-radius: 12px;
            font-weight: 500;

            &::before {
              content: "相关度";
              color: var(--text-color-secondary);
              font-size: 11px;
            }

            &::after {
              content: "%";
              color: var(--primary-color);
              font-weight: 600;
            }
          }
        }
      }

    .reasoning {
      margin-top: 16px;
      margin-bottom: 12px;

      .reasoning-header {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 13px;
        color: var(--text-color-secondary);
        margin-bottom: 12px;
        font-weight: 500;
      }

      .thought-chain-container {
        .thought-chain-component {
          border-radius: 8px;
          background: var(--bg-color);
          border: 1px solid var(--border-color-lighter);
          padding: 16px;
          box-shadow: var(--box-shadow-sm);

          :deep(.el-timeline-item) {
            margin-bottom: 12px;

            &:last-child {
              margin-bottom: 0;
            }
          }

          :deep(.thought-title) {
            font-weight: 500;
            color: var(--text-color-primary);
            margin-bottom: 6px;
          }

          :deep(.thought-content) {
            font-size: 13px;
            line-height: 1.5;
            color: var(--text-color-regular);
          }

          :deep(.thought-timeline) {
            border-left: 2px solid var(--primary-color);
            padding-left: 12px;
          }

          // Element-Plus-X ThoughtChain组件主题适配（基于实际DOM结构）

        }
      }
    }
    }
  }
}
:deep(.el-thought-chain) {
  background: var(--bg-color);
  color: var(--text-color-primary);
  border: 1px solid var(--border-color-lighter);
  border-radius: 8px;
  padding: 16px;

  // 时间轴容器
  .el-timeline {
    background: transparent;

    // 时间轴项目
    .el-timeline-item {
      // 时间轴连接线
      .el-timeline-item__tail {
        border-left: 2px solid var(--primary-color);
      }

      // 思维链节点圆点
      .el-timeline-item__dot {
        .el-thought-chain-item-dot {
          .el-button {
            background-color: var(--primary-color) !important;
            border-color: var(--primary-color) !important;
            color: var(--bg-color);

            &:hover {
              background-color: var(--primary-hover) !important;
              border-color: var(--primary-hover) !important;
            }

            .el-icon {
              color: var(--bg-color);
            }
          }
        }
      }

      // 时间戳标题
      .el-timeline-item__timestamp {
        color: var(--text-color-primary);
        font-weight: 500;
        font-size: 14px;

        &.is-top {
          color: var(--text-color-primary);
        }
      }

      // 内容区域
      .el-timeline-item__content {
        background: var(--fill-color);
        border: 1px solid var(--border-color-lighter);
        border-radius: 8px;
        padding: 12px;
        margin-top: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--fill-color-light);
          border-color: var(--border-color-light);
          box-shadow: var(--box-shadow-sm);
        }

        // 折叠面板
        .el-collapse {
          background: transparent;
          border: none;

          .el-collapse-item {
            .el-collapse-item__header {
              background: transparent;
              color: var(--text-color-primary);
              border-bottom: 1px solid var(--border-color-lighter);
              padding: 8px 0;

              .el-collapse-item__title {
                color: var(--text-color-primary);
                font-weight: 500;
              }

              .el-collapse-item__arrow {
                color: var(--text-color-secondary);
              }

              &:hover {
                background: var(--fill-color-light);
              }
            }

            .el-collapse-item__wrap {
              background: transparent;
              border: none;

              .el-collapse-item__content {
                padding: 12px 0 0 0;
                color: var(--text-color-regular);
                line-height: 1.5;
              }
            }
          }
        }

        // 打字机效果容器
        .typer-container {
          .typer-content {
            color: var(--text-color-regular);
            line-height: 1.5;
            font-size: 14px;
          }
        }
      }
    }
  }
}
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes blink {
  0%, 50% {
    opacity: 1;
  }
  51%, 100% {
    opacity: 0;
  }
}

// 暗色主题特殊适配
@media (prefers-color-scheme: dark) {
  .chat-message {
    .user-message {
      .user-avatar {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        border-color: var(--primary-light);
        box-shadow: 0 2px 8px rgba(139, 92, 246, 0.3);
      }

      .message-content {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
        box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.3);
        border-color: var(--primary-light);
        color: #ffffff;
      }
    }

    .ai-message {
      .message-content {
        background: var(--fill-color-dark);
        border-color: var(--border-color);

        .thinking-section {
          .thinking-component {
            background: var(--fill-color-dark);
            border-color: var(--border-color);

            // 暗色主题下的Thinking组件特殊适配
            :deep(.el-thinking) {
              border-color: var(--border-color);

              .trigger {
                background: var(--fill-color-dark);
                color: var(--text-color-primary);

                &:hover {
                  background: var(--fill-color-darker);
                }

                .label {
                  color: var(--text-color-primary);
                }

                .arrow {
                  color: var(--text-color-secondary);
                }
              }

              .content-wrapper {
                background: var(--fill-color-darker);
                border-top-color: var(--border-color);

                .content {
                  color: var(--text-color-regular);

                  pre {
                    color: var(--text-color-regular);
                  }
                }
              }
            }
          }
        }

        // 暗色主题下的参考资料特殊适配
        .sources {
          background: var(--fill-color-dark);
          border-color: var(--border-color);

          .sources-header {
            border-bottom-color: var(--border-color);
            color: var(--text-color-primary);

            .el-icon {
              color: var(--primary-color);
            }
          }

          .sources-list {
            :deep(.el-collapse) {
              .el-collapse-item {
                background: var(--fill-color-darker);
                border-color: var(--border-color);

                &:hover {
                  border-color: var(--border-color-light);
                  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.15);
                }

                &.is-active {
                  border-color: var(--primary-light);
                  box-shadow: 0 2px 12px rgba(139, 92, 246, 0.2);
                }

                .el-collapse-item__header {
                  background: var(--fill-color-dark);
                  color: var(--text-color-primary);

                  &:hover {
                    background: var(--fill-color-darker);
                    color: var(--primary-color);
                  }

                  .el-collapse-item__arrow {
                    color: var(--text-color-secondary);

                    &.is-active {
                      color: var(--primary-color);
                    }
                  }
                }

                .el-collapse-item__wrap {
                  background: var(--fill-color-darker);
                  border-top-color: var(--border-color);

                  .el-collapse-item__content {
                    background: var(--fill-color-darker);
                  }
                }
              }
            }
          }

          .source-content {
            .source-text {
              background: var(--fill-color-darker);
              color: var(--text-color-regular);
              border-left-color: var(--primary-light);
            }

            .source-score {
              background: var(--fill-color-darker);
              color: var(--text-color-secondary);

              &::before {
                color: var(--text-color-secondary);
              }

              &::after {
                color: var(--primary-color);
              }
            }
          }
        }

        .reasoning {
          .thought-chain-container {
            .thought-chain-component {
              background: var(--fill-color-dark);
              border-color: var(--border-color);

              // 暗色主题下的ThoughtChain特殊适配
              :deep(.el-thought-chain) {
                background: var(--fill-color-dark);
                border-color: var(--border-color);

                .el-timeline {
                  .el-timeline-item {
                    .el-timeline-item__content {
                      background: var(--fill-color-darker);
                      border-color: var(--border-color);

                      &:hover {
                        background: var(--fill-color-dark);
                        border-color: var(--border-color-light);
                      }

                      .el-collapse {
                        .el-collapse-item {
                          .el-collapse-item__header {
                            border-bottom-color: var(--border-color);

                            &:hover {
                              background: var(--fill-color-dark);
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
  }
}

// Element-Plus-X 组件通用主题适配
:deep(.epx-component) {
  // 通用背景和文本颜色
  background: var(--bg-color);
  color: var(--text-color-primary);
  border-color: var(--border-color-lighter);

  // 通用链接颜色
  a {
    color: var(--primary-color);

    &:hover {
      color: var(--primary-hover);
    }
  }

  // 通用按钮样式
  .epx-button {
    background: var(--primary-color);
    color: #ffffff;
    border-color: var(--primary-color);

    &:hover {
      background: var(--primary-hover);
      border-color: var(--primary-hover);
    }

    &:disabled {
      background: var(--fill-color-light);
      color: var(--text-color-disabled);
      border-color: var(--border-color-light);
    }
  }

  // 通用输入框样式
  .epx-input {
    background: var(--bg-color);
    color: var(--text-color-primary);
    border-color: var(--border-color-light);

    &::placeholder {
      color: var(--text-color-placeholder);
    }

    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px var(--primary-lighter);
    }
  }

  // 通用卡片样式
  .epx-card {
    background: var(--bg-color);
    border-color: var(--border-color-lighter);
    box-shadow: var(--box-shadow-sm);

    .epx-card-header {
      background: var(--fill-color-lighter);
      border-bottom: 1px solid var(--border-color-lighter);
      color: var(--text-color-primary);
    }

    .epx-card-body {
      color: var(--text-color-regular);
    }
  }
}

// 自定义滚动条样式
.chat-message {
  // Webkit浏览器滚动条样式（Chrome、Safari、Edge）
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--fill-color-lighter);
    border-radius: 4px;
    margin: 2px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-color-light);
    border-radius: 4px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--primary-color);
      transform: scaleY(1.1);
    }

    &:active {
      background: var(--primary-hover);
    }
  }

  ::-webkit-scrollbar-corner {
    background: var(--fill-color-lighter);
  }

  // Firefox浏览器滚动条样式
  scrollbar-width: thin;
  scrollbar-color: var(--border-color-light) var(--fill-color-lighter);

  &:hover {
    scrollbar-color: var(--primary-color) var(--fill-color-lighter);
  }
}

// AI消息内容区域滚动条
.ai-message {
  .message-content {
    ::-webkit-scrollbar {
      width: 6px;
      height: 6px;
    }

    ::-webkit-scrollbar-track {
      background: var(--fill-color-lighter);
      border-radius: 3px;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--border-color-lighter);
      border-radius: 3px;
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-light);
      }
    }

    scrollbar-width: thin;
    scrollbar-color: var(--border-color-lighter) var(--fill-color-lighter);
  }
}

// ThoughtChain组件滚动条
:deep(.el-thought-chain) {
  ::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  ::-webkit-scrollbar-track {
    background: var(--fill-color-lighter);
    border-radius: 3px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--border-color-light);
    border-radius: 3px;
    transition: all 0.3s ease;

    &:hover {
      background: var(--primary-color);
    }
  }

  scrollbar-width: thin;
  scrollbar-color: var(--border-color-light) var(--fill-color-lighter);

  .el-timeline-item__content {
    ::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    ::-webkit-scrollbar-track {
      background: transparent;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--border-color-lighter);
      border-radius: 2px;

      &:hover {
        background: var(--primary-light);
      }
    }

    scrollbar-width: thin;
    scrollbar-color: var(--border-color-lighter) transparent;
  }
}

// Thinking组件滚动条
:deep(.el-thinking) {
  .content-wrapper {
    .content {
      ::-webkit-scrollbar {
        width: 4px;
        height: 4px;
      }

      ::-webkit-scrollbar-track {
        background: var(--fill-color-lighter);
        border-radius: 2px;
      }

      ::-webkit-scrollbar-thumb {
        background: var(--border-color-lighter);
        border-radius: 2px;
        transition: all 0.3s ease;

        &:hover {
          background: var(--primary-light);
        }
      }

      scrollbar-width: thin;
      scrollbar-color: var(--border-color-lighter) var(--fill-color-lighter);
    }
  }
}

// 响应式设计 - 移动端适配
@media (max-width: 768px) {
  .chat-message {
    // 移动端滚动条样式优化
    ::-webkit-scrollbar {
      width: 4px;
      height: 4px;
    }

    ::-webkit-scrollbar-track {
      background: transparent;
    }

    ::-webkit-scrollbar-thumb {
      background: var(--border-color-lighter);
      border-radius: 2px;
      transition: all 0.3s ease;

      &:hover {
        background: var(--primary-light);
      }
    }

    scrollbar-width: thin;
    scrollbar-color: var(--border-color-lighter) transparent;

    &.user-message-wrapper {
      .user-message {
        max-width: 85%;
        gap: 8px;

        .message-content {
          max-width: calc(100% - 50px);
          padding: 10px 14px;
          font-size: 14px;

          .mode-tag {
            margin-top: 6px;

            .el-tag {
              font-size: 10px;
              padding: 2px 6px;
            }
          }
        }

        .message-header {
          .message-info {
            .sender-name {
              font-size: 13px;
            }

            .message-time {
              font-size: 11px;
            }
          }
        }

        .user-avatar {
          width: 28px;
          height: 28px;
          font-size: 14px;
        }
      }
    }

    // 移动端参考资料样式优化
    .ai-message {
      .message-content {
        .sources {
          padding: 10px;
          margin-bottom: 12px;

          .sources-header {
            font-size: 13px;
            margin-bottom: 10px;
            padding-bottom: 6px;

            .el-icon {
              font-size: 14px;
            }
          }

          .sources-list {
            :deep(.el-collapse) {
              .el-collapse-item {
                margin-bottom: 6px;

                .el-collapse-item__header {
                  padding: 10px 12px;
                  font-size: 12px;

                  .el-collapse-item__arrow {
                    font-size: 11px;
                  }
                }

                .el-collapse-item__wrap {
                  .el-collapse-item__content {
                    padding: 12px;
                  }
                }
              }
            }
          }

          .source-content {
            .source-text {
              font-size: 12px;
              line-height: 1.5;
              margin-bottom: 10px;
              padding: 6px 10px;
            }

            .source-score {
              font-size: 11px;
              padding: 3px 6px;

              &::before {
                font-size: 10px;
              }
            }
          }
        }
      }
    }
  }
}

// 暗色主题滚动条特殊适配
@media (prefers-color-scheme: dark) {
  .chat-message {
    ::-webkit-scrollbar-track {
      background: var(--fill-color-dark);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--border-color);

      &:hover {
        background: var(--primary-color);
        box-shadow: 0 0 6px rgba(139, 92, 246, 0.3);
      }

      &:active {
        background: var(--primary-hover);
      }
    }

    scrollbar-color: var(--border-color) var(--fill-color-dark);

    &:hover {
      scrollbar-color: var(--primary-color) var(--fill-color-dark);
    }
  }

  .ai-message {
    .message-content {
      ::-webkit-scrollbar-track {
        background: var(--fill-color-darker);
      }

      ::-webkit-scrollbar-thumb {
        background: var(--border-color);

        &:hover {
          background: var(--primary-light);
          box-shadow: 0 0 4px rgba(139, 92, 246, 0.2);
        }
      }

      scrollbar-color: var(--border-color) var(--fill-color-darker);
    }
  }

  :deep(.el-thought-chain) {
    ::-webkit-scrollbar-track {
      background: var(--fill-color-dark);
    }

    ::-webkit-scrollbar-thumb {
      background: var(--border-color);

      &:hover {
        background: var(--primary-color);
        box-shadow: 0 0 4px rgba(139, 92, 246, 0.2);
      }
    }

    scrollbar-color: var(--border-color) var(--fill-color-dark);

    .el-timeline-item__content {
      ::-webkit-scrollbar-thumb {
        background: var(--border-color-light);

        &:hover {
          background: var(--primary-light);
        }
      }

      scrollbar-color: var(--border-color-light) transparent;
    }
  }

  :deep(.el-thinking) {
    .content-wrapper {
      .content {
        ::-webkit-scrollbar-track {
          background: var(--fill-color-darker);
        }

        ::-webkit-scrollbar-thumb {
          background: var(--border-color);

          &:hover {
            background: var(--primary-light);
            box-shadow: 0 0 3px rgba(139, 92, 246, 0.2);
          }
        }

        scrollbar-color: var(--border-color) var(--fill-color-darker);
      }
    }
  }
}
</style>
