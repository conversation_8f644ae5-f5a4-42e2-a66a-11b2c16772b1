<template>
  <div class="chat-input">
    <!-- 使用Element-Plus-X的智能输入框组件 -->
    <Sender
        ref="senderRef"
      v-model="inputValue"
      :placeholder="inputPlaceholder"
      :disabled="disabled"
      :loading="loading"
      @send="handleSend"
      @stop="handleStop"
      class="ai-input-component"
    >
      <!-- 头部工具栏 -->
      <template #header>
        <div class="input-toolbar">
          <!-- 左侧功能区域 -->
          <div class="toolbar-left">
            <!-- 对话模式切换 -->
            <div class="mode-selector">
              <el-radio-group v-model="currentMode" size="small" @change="handleModeChange">
                <el-radio-button value="chat">
                  <el-icon><ChatDotRound /></el-icon>
                  普通对话
                </el-radio-button>
                <el-radio-button value="report">
                  <el-icon><Document /></el-icon>
                  编报模式
                </el-radio-button>
              </el-radio-group>
            </div>

            <!-- 知识库选择 -->
            <div class="knowledge-selector">
              <el-select
                v-model="selectedKnowledgeBase"
                placeholder="选择知识库"
                size="small"
                @change="handleKnowledgeBaseChange"
              >
                <el-option
                  label="全部知识库"
                  value=""
                />
                <el-option
                  v-for="kb in knowledgeBases"
                  :key="kb.value"
                  :label="kb.label"
                  :value="kb.value"
                />
              </el-select>
            </div>
          </div>

          <!-- 右侧操作区域 -->
          <div class="toolbar-right">
            <!-- 清空对话 -->
            <div class="clear-button">
              <el-button
                size="small"
                plain
                @click="handleClear"
                :disabled="loading || !hasMessages"
              >
                <el-icon><Delete /></el-icon>
                清空对话
              </el-button>
            </div>
          </div>
        </div>
      </template>

      <!-- 操作按钮列表 -->
      <template #action-list>
        <el-button
          v-if="!loading"
          type="primary"
          :disabled="!canSend"
          @click="handleSend"
          class="send-button"
        >
          <el-icon><Promotion /></el-icon>
          发送
        </el-button>
        <el-button
          v-else
          type="danger"
          @click="handleStop"
          class="stop-button"
        >
          <el-icon><VideoPause /></el-icon>
          停止
        </el-button>
      </template>
    </Sender>

  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Promotion,
  VideoPause,
  ChatDotRound,
  Document,
  Delete
} from '@element-plus/icons-vue'
import { useChatStore } from '@/stores/chat'
import { knowledgeList } from '@/api/knowledge.js'
import { Sender } from 'vue-element-plus-x'

// 组件属性
const props = defineProps({
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  maxLength: {
    type: Number,
    default: 2000
  }
})
const senderRef = ref(null)
// 组件事件
const emit = defineEmits(['send', 'stop', 'clear', 'mode-change', 'knowledge-base-change'])

// 状态管理
const chatStore = useChatStore()

// 响应式数据
const inputValue = ref('')
const knowledgeBases = ref([])

// 计算属性
const currentMode = computed({
  get: () => chatStore.currentMode,
  set: (value) => chatStore.setMode(value)
})

const selectedKnowledgeBase = computed({
  get: () => chatStore.selectedKnowledgeBase,
  set: (value) => chatStore.setKnowledgeBase(value)
})

const hasMessages = computed(() => chatStore.hasMessages)

const canSend = computed(() => {
  return inputValue.value.trim().length > 0 && !props.disabled
})

const inputPlaceholder = computed(() => {
  if (currentMode.value === 'chat') {
    return '请输入您的问题...'
  } else {
    return '请输入报告标题或主题...'
  }
})


// 方法
const handleSend = () => {
  const content = inputValue.value.trim()
  if (!content) {
    ElMessage.warning('请输入内容')
    return
  }

  const params = {
    content,
    mode: currentMode.value,
    knowledgeBase: selectedKnowledgeBase.value
  }

  emit('send', params)
  inputValue.value = ''
}

const handleStop = () => {
  emit('stop')
}

const handleClear = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有对话记录吗？此操作不可恢复。',
      '清空对话',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    emit('clear')
  } catch {
    // 用户取消
  }
}

const handleModeChange = (mode) => {
  emit('mode-change', mode)
}

const handleKnowledgeBaseChange = (knowledgeBase) => {
  emit('knowledge-base-change', knowledgeBase)
}


// 加载知识库列表
const loadKnowledgeBases = async () => {
  try {
    const result = await knowledgeList()
    knowledgeBases.value = result.data.map(kb => ({
      label: kb.name,
      value: kb.name_md5
    }))
  } catch (error) {
    console.error('加载知识库列表失败:', error)
    ElMessage.error('加载知识库列表失败')
  }
}


// 组件挂载时加载知识库
loadKnowledgeBases()
onMounted(()=>{
  // console.log('组件已挂载')
  senderRef.value.focus()
  senderRef.value.openHeader()
})
</script>

<style lang="scss" scoped>
.chat-input {
  .ai-input-component {
    :deep(.sender-container) {
      border-radius: 12px;
      box-shadow: var(--box-shadow-light);
      background: var(--bg-color);
      border: 1px solid var(--border-color-lighter);
    }
    background: transparent;
    border: none;
    :deep(.el-sender) {
      background: var(--bg-color);
      border: 1px solid var(--border-color-lighter);
      border-radius: 12px;
      box-shadow: var(--box-shadow-sm);
      transition: all 0.3s ease;

      &:hover {
        border-color: var(--border-color-light);
        box-shadow: var(--box-shadow);
      }

      // 头部区域
      .el-sender-header-wrap {
        .el-sender-header {
          background: var(--bg-color-page);
          border-bottom: 1px solid var(--border-color-lighter);
          border-radius: 12px 12px 0 0;
          padding: 0;
        }
      }

      // 内容区域
      .el-sender-content {
        background: var(--bg-color);
        padding: 12px 16px;
        border-radius: 0 0 12px 12px;

        // 输入框区域
        .el-sender-input {
          background: transparent;
          border: none;
          margin-bottom: 12px;

          .el-textarea__inner {
            background: var(--bg-color);
            color: var(--text-color-primary);
            border: 1px solid var(--border-color-light);
            border-radius: 8px;
            padding: 12px;
            font-size: 14px;
            line-height: 1.5;
            resize: none;
            transition: all 0.3s ease;

            &::placeholder {
              color: var(--text-color-placeholder);
            }

            &:focus {
              border-color: var(--primary-color);
              box-shadow: 0 0 0 2px var(--primary-lighter);
              outline: none;
            }

            &:hover {
              border-color: var(--border-color);
            }
          }
        }

        // 操作按钮区域
        .el-sender-action-list {
          display: flex;
          justify-content: flex-end;
          gap: 8px;

          .send-button {
            background: var(--primary-color);
            color: #ffffff;
            border-color: var(--primary-color);
            border-radius: 8px;
            padding: 8px 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin-bottom: 15px;

            &:hover {
              background: var(--primary-hover);
              border-color: var(--primary-hover);
              transform: translateY(-1px);
              box-shadow: 0 4px 12px rgba(99, 102, 241, 0.3);
            }

            &:active {
              transform: translateY(0);
            }

            &:disabled {
              background: var(--fill-color-light);
              color: var(--text-color-disabled);
              border-color: var(--border-color-light);
              transform: none;
              box-shadow: none;
              cursor: not-allowed;
            }

            .el-icon {
              margin-right: 4px;
              font-size: 14px;
            }
          }
        }
      }
    }
    // Element-Plus-X Sender组件主题适配（基于实际DOM结构）
    //:deep(.el-sender-wrap) {
    //
    //
    //
    //}
  }

  .input-toolbar {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    padding: 16px 20px;
    background: linear-gradient(135deg, var(--bg-color-page) 0%, var(--fill-color-lighter) 100%);
    border-bottom: 1px solid var(--border-color-lighter);
    border-radius: 12px 12px 0 0;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    position: relative;

    // 添加装饰性渐变线
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 2px;
      background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--primary-color) 100%);
      opacity: 0.6;
    }

    // 左侧功能区域
    .toolbar-left {
      display: flex;
      align-items: center;
      gap: 16px;
      flex: 1;
    }

    // 右侧操作区域
    .toolbar-right {
      display: flex;
      align-items: center;
      gap: 12px;
      flex-shrink: 0;
    }

    // 对话模式切换器优化
    .mode-selector {
      :deep(.el-radio-group) {
        background: var(--bg-color);
        border: 1px solid var(--border-color-lighter);
        border-radius: 8px;
        padding: 2px;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

        .el-radio-button {
          margin: 0;

          &:first-child .el-radio-button__inner {
            border-radius: 6px 0 0 6px;
            border-left: none;
          }

          &:last-child .el-radio-button__inner {
            border-radius: 0 6px 6px 0;
            border-right: none;
          }

          .el-radio-button__inner {
            display: flex;
            align-items: center;
            gap: 6px;
            padding: 8px 12px;
            background: transparent;
            border: none;
            color: var(--text-color-regular);
            font-weight: 500;
            font-size: 13px;
            transition: all 0.3s ease;
            position: relative;

            &:hover {
              background: var(--fill-color-light);
              color: var(--primary-color);
            }

            .el-icon {
              font-size: 14px;
              transition: all 0.3s ease;
            }
          }

          &.is-active .el-radio-button__inner {
            background: var(--primary-color);
            color: #ffffff;
            box-shadow: 0 2px 6px rgba(99, 102, 241, 0.3);
            transform: translateY(-1px);

            .el-icon {
              color: #ffffff;
            }
          }
        }
      }
    }

    // 知识库选择器优化
    .knowledge-selector {
      :deep(.el-select) {
        .el-select__wrapper {
          background: var(--bg-color);
          border: 1px solid var(--border-color-lighter);
          border-radius: 8px;
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
          transition: all 0.3s ease;
          min-width: 180px;

          &:hover {
            border-color: var(--border-color-light);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
          }

          &.is-focused {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 2px var(--primary-lighter);
          }

          .el-select__selected-item {
            color: var(--text-color-primary);
            font-weight: 500;
            font-size: 13px;
          }

          .el-select__placeholder {
            color: var(--text-color-placeholder);
            font-size: 13px;
          }

          .el-select__suffix {
            .el-icon {
              color: var(--text-color-secondary);
              transition: all 0.3s ease;
            }
          }

          &.is-focused .el-select__suffix .el-icon {
            color: var(--primary-color);
            transform: rotate(180deg);
          }
        }
      }
    }

    // 清空对话按钮优化
    .clear-button {
      :deep(.el-button) {
        background: var(--bg-color);
        border: 1px solid var(--border-color-lighter);
        color: var(--text-color-regular);
        border-radius: 8px;
        padding: 8px 12px;
        font-weight: 500;
        font-size: 13px;
        transition: all 0.3s ease;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);

        &:hover:not(.is-disabled) {
          background: var(--fill-color-light);
          border-color: var(--border-color-light);
          color: var(--primary-color);
          transform: translateY(-1px);
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);

          .el-icon {
            color: var(--primary-color);
          }
        }

        &:active:not(.is-disabled) {
          transform: translateY(0);
        }

        &.is-disabled {
          background: var(--fill-color-lighter);
          border-color: var(--border-color-lighter);
          color: var(--text-color-disabled);
          cursor: not-allowed;
          transform: none;
          box-shadow: none;

          .el-icon {
            color: var(--text-color-disabled);
          }
        }

        .el-icon {
          font-size: 14px;
          margin-right: 4px;
          color: var(--text-color-secondary);
          transition: all 0.3s ease;
        }
      }
    }
  }

  .send-button,
  .stop-button {
    display: flex;
    align-items: center;
    gap: 4px;
    min-width: 80px;
  }

}

// 响应式设计
@media (max-width: 768px) {
  .chat-input {
    .input-toolbar {
      flex-direction: column;
      align-items: stretch;
      gap: 16px;
      padding: 16px 18px;
      background: var(--bg-color-page);

      // 移动端移除装饰线
      &::after {
        display: none;
      }

      // 左侧功能区域移动端布局
      .toolbar-left {
        flex-direction: column;
        gap: 12px;
        width: 100%;
      }

      // 右侧操作区域移动端布局
      .toolbar-right {
        width: 100%;
        justify-content: center;
      }

      // 对话模式切换器移动端优化
      .mode-selector {
        :deep(.el-radio-group) {
          width: 100%;
          display: flex;

          .el-radio-button {
            flex: 1;

            .el-radio-button__inner {
              width: 100%;
              justify-content: center;
              padding: 10px 8px;
              font-size: 12px;

              .el-icon {
                font-size: 16px;
              }
            }
          }
        }
      }

      // 知识库选择器移动端优化
      .knowledge-selector {
        :deep(.el-select) {
          width: 100%;

          .el-select__wrapper {
            width: 100%;
            min-width: auto;
            padding: 10px 12px;

            .el-select__selected-item,
            .el-select__placeholder {
              font-size: 14px;
            }
          }
        }
      }

      // 清空按钮移动端优化
      .clear-button {
        :deep(.el-button) {
          width: 100%;
          padding: 10px 16px;
          font-size: 14px;
          justify-content: center;

          .el-icon {
            font-size: 16px;
            margin-right: 6px;
          }
        }
      }
    }
  }
}

// 暗色主题特殊适配
@media (prefers-color-scheme: dark) {
  .chat-input {
    .ai-input-component {
      :deep(.el-sender-wrap) {
        .el-sender {
          background: var(--fill-color-dark);
          border-color: var(--border-color);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);

          &:hover {
            border-color: var(--border-color-light);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.2);
          }

          // 头部区域暗色主题
          .el-sender-header-wrap {
            .el-sender-header {
              background: var(--fill-color-darker);
              border-bottom-color: var(--border-color);
            }
          }

          // 内容区域暗色主题
          .el-sender-content {
            background: var(--fill-color-dark);

            // 输入框暗色主题
            .el-sender-input {
              .el-textarea__inner {
                background: var(--fill-color-darker);
                color: var(--text-color-primary);
                border-color: var(--border-color);

                &::placeholder {
                  color: var(--text-color-placeholder);
                }

                &:focus {
                  border-color: var(--primary-color);
                  box-shadow: 0 0 0 2px var(--primary-lighter);
                }

                &:hover {
                  border-color: var(--border-color-light);
                }
              }
            }

            // 按钮暗色主题
            .el-sender-action-list {
              .send-button {
                background: var(--primary-color);
                border-color: var(--primary-color);

                &:hover {
                  background: var(--primary-hover);
                  border-color: var(--primary-hover);
                  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.4);
                }

                &:disabled {
                  background: var(--fill-color-darker);
                  color: var(--text-color-disabled);
                  border-color: var(--border-color);
                }
              }
            }
          }
        }
      }
    }

    // 工具栏暗色主题
    .input-toolbar {
      background: linear-gradient(135deg, var(--fill-color-dark) 0%, var(--fill-color-darker) 100%);
      border-bottom-color: var(--border-color);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);

      // 暗色主题装饰线
      &::after {
        background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-light) 50%, var(--primary-color) 100%);
        opacity: 0.8;
      }

      // 对话模式切换器暗色主题
      .mode-selector {
        :deep(.el-radio-group) {
          background: var(--fill-color-darker);
          border-color: var(--border-color);
          box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.2);

          .el-radio-button {
            .el-radio-button__inner {
              color: var(--text-color-regular);

              &:hover {
                background: var(--fill-color-dark);
                color: var(--primary-color);
              }
            }

            &.is-active .el-radio-button__inner {
              background: var(--primary-color);
              color: #ffffff;
              box-shadow: 0 2px 8px rgba(139, 92, 246, 0.4);
            }
          }
        }
      }

      // 知识库选择器暗色主题
      .knowledge-selector {
        :deep(.el-select) {
          .el-select__wrapper {
            background: var(--fill-color-darker);
            border-color: var(--border-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

            &:hover {
              border-color: var(--border-color-light);
              box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
            }

            &.is-focused {
              border-color: var(--primary-color);
              box-shadow: 0 0 0 2px var(--primary-lighter);
            }

            .el-select__selected-item {
              color: var(--text-color-primary);
            }

            .el-select__placeholder {
              color: var(--text-color-placeholder);
            }

            .el-select__suffix .el-icon {
              color: var(--text-color-secondary);
            }

            &.is-focused .el-select__suffix .el-icon {
              color: var(--primary-color);
            }
          }
        }
      }

      // 清空按钮暗色主题
      .clear-button {
        :deep(.el-button) {
          background: var(--fill-color-darker);
          border-color: var(--border-color);
          color: var(--text-color-regular);
          box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

          &:hover:not(.is-disabled) {
            background: var(--fill-color-dark);
            border-color: var(--border-color-light);
            color: var(--primary-color);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);

            .el-icon {
              color: var(--primary-color);
            }
          }

          &.is-disabled {
            background: var(--fill-color-darker);
            border-color: var(--border-color);
            color: var(--text-color-disabled);

            .el-icon {
              color: var(--text-color-disabled);
            }
          }

          .el-icon {
            color: var(--text-color-secondary);
          }
        }
      }
    }
  }
}
</style>
<style lang="scss">
// 知识库选项样式 - 主题适配
.el-select-dropdown {
  border-radius: 12px;
  box-shadow: var(--box-shadow-lg);
  border: 1px solid var(--border-color-lighter);
  padding: 8px;
  background: var(--bg-color) !important; // 主题背景适配

  .el-select-dropdown__item {
    border-radius: 8px;
    margin-bottom: 4px;
    padding-left: 6px;
    transition: all 0.2s ease;
    color: var(--text-color-primary) !important; // 主题文字色彩
    background: transparent;

    &:last-child {
      margin-bottom: 0;
    }

    &:hover {
      background: var(--primary-lighter) !important;
      color: var(--primary-color) !important;
    }

    &.is-selected {
      background: var(--primary-color) !important;
      color: white !important;

      .kb-option-content {
        .kb-option-main {
          .kb-icon {
            color: white !important;
          }

          .kb-count {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            border-color: transparent !important;
          }
        }

        .kb-description {
          color: rgba(255, 255, 255, 0.8) !important;
        }
      }

      // 集成式选项样式
      .kb-option-integrated {
        .kb-icon {
          color: white !important;
        }

        .kb-name {
          color: white !important;
        }

        .kb-count {
          background: rgba(255, 255, 255, 0.2) !important;
          color: white !important;
          border-color: transparent !important;
        }
      }
    }
  }
}

</style>
