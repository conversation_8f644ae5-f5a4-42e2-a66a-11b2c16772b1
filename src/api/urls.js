/**
 * API地址管理
 * 简洁的API地址定义，便于维护和修改
 */

// 服务前缀定义
const knowledge_service_prefix = '/v1/'

// API地址定义
export default {
  // ==================== 认证相关 ====================
  captcha: `${knowledge_service_prefix}auth/captcha`,
  login: `${knowledge_service_prefix}auth/login`,
  logout: `${knowledge_service_prefix}auth/logout`,
  refreshToken: `${knowledge_service_prefix}auth/refresh`,
  forgotPassword: `${knowledge_service_prefix}auth/forgot-password`,
  resetPassword: `${knowledge_service_prefix}auth/reset-password`,
  verifyToken: `${knowledge_service_prefix}auth/verify`,

  // ==================== 搜索相关 ====================
  fullTextSearch: `${knowledge_service_prefix}intelligent_search/text_search`,
  intelligentSearch: `${knowledge_service_prefix}intelligent_search/search`,

  // ==================== 知识库相关 ====================
  knowledgeList: `${knowledge_service_prefix}knowledge/list`,
  knowledgeBases: `${knowledge_service_prefix}knowledge/list`,
  createKnowledge: `${knowledge_service_prefix}knowledge/create`,
  documentList: `${knowledge_service_prefix}document/list`,
  uploadDocument: `${knowledge_service_prefix}document/upload`,
  deleteCategory:`${knowledge_service_prefix}knowledge/delete`,
  deleteCurrentDocument:`${knowledge_service_prefix}document/delete`,

  // ==================== AI对话相关 ====================
  chat: `${knowledge_service_prefix}knowledge/chat`,
  reportGenerate: `${knowledge_service_prefix}knowledge/generate_report`,
}


