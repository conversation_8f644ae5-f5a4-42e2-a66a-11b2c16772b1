import {request} from "@/utils/request.js"
import urls from "@/api/urls.js"
/**
 * 文本搜索
 * @param params
 * params: {
 *     collection_name: string,
 *     query: string,
 * }
 */
export const fullTextSearch = (params) => {
    return request({
        url: urls.fullTextSearch,
        method: 'get',
        params
    })
}

/**
 * 智能搜索
 * @param params
 * params: {
 *     collection_name: string,
 *     query: string,
 * }
 */
export const intelligentSearch = (params) => {
    return request({
        url: urls.intelligentSearch,
        method: 'get',
        params
    })
}

/**
 * 获取知识库列表
 * @returns {Promise} 知识库列表
 * 返回格式: [
 *   {
 *     label: string,    // 知识库显示名称
 *     value: string,    // 知识库唯一标识
 *     count: number     // 文档数量
 *   }
 * ]
 */
export const getKnowledgeBases = () => {
    return request({
        url: urls.knowledgeBases,
        method: 'get'
    })
}
