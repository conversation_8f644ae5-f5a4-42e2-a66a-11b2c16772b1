import { request } from '@/utils/request.js'
import urls from '@/api/urls.js'
import {SSE} from 'sse.js'

/**
 * AI对话API接口
 */

/**
 * 普通对话模式 - SSE流式响应
 * @param {Object} params 对话参数
 * @param {string} params.question 用户问题
 * @param {string} params.collection_name 知识库名称
 * @param {Function} onMessage SSE消息回调函数
 * @param {Function} onError 错误回调函数
 * @param {Function} onComplete 完成回调函数
 * @returns {Object} SSE连接对象，可用于关闭连接
 */
export const chatWithSSE = (params, onMessage, onError, onComplete) => {
  // 构建SSE请求URL
  const baseURL = import.meta.env.VITE_BASE_API || '/api'
  const url = `${baseURL}${urls.chat}`

  // 创建SSE连接
  const eventSource = new SSE(url, {
    headers: {
      'Content-Type': 'application/json',
    },
    payload: JSON.stringify(params),
    method: 'POST'
  })

  // 监听不同类型的SSE事件
  eventSource.addEventListener('thinking', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('thinking', data)
    } catch (error) {
      console.error('解析thinking事件数据失败:', error)
    }
  })

  eventSource.addEventListener('assessment', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('assessment', data)
    } catch (error) {
      console.error('解析assessment事件数据失败:', error)
    }
  })

  eventSource.addEventListener('searching', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('searching', data)
    } catch (error) {
      console.error('解析searching事件数据失败:', error)
    }
  })

  eventSource.addEventListener('found_sources', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('found_sources', data)
    } catch (error) {
      console.error('解析found_sources事件数据失败:', error)
    }
  })

  eventSource.addEventListener('generating', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('generating', data)
    } catch (error) {
      console.error('解析generating事件数据失败:', error)
    }
  })

  eventSource.addEventListener('streaming', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('streaming', data)
    } catch (error) {
      console.error('解析streaming事件数据失败:', error)
    }
  })

  eventSource.addEventListener('completed', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('completed', data)
      onComplete && onComplete(data)
    } catch (error) {
      console.error('解析completed事件数据失败:', error)
    }
  })

  // 监听连接打开事件
  eventSource.addEventListener('open', () => {
    console.log('SSE连接已建立')
  })

  // 监听错误事件
  eventSource.addEventListener('error', (event) => {
    console.error('SSE连接错误:', event)
    onError && onError(event)
  })

  return eventSource
}

/**
 * 编报模式 - SSE流式响应
 * @param {Object} params 编报参数
 * @param {string} params.title 报告标题
 * @param {string} params.collection_name 知识库名称
 * @param {Function} onMessage SSE消息回调函数
 * @param {Function} onError 错误回调函数
 * @param {Function} onComplete 完成回调函数
 * @returns {Object} SSE连接对象，可用于关闭连接
 */
export const generateReportWithSSE = (params, onMessage, onError, onComplete) => {
  // 构建SSE请求URL
  const baseURL = import.meta.env.VITE_BASE_API || '/api'
  const url = `${baseURL}${urls.reportGenerate}`

  // 创建SSE连接
  const eventSource = new SSE(url, {
    headers: {
      'Content-Type': 'application/json',
    },
    payload: JSON.stringify(params),
    method: 'POST'
  })

  // 监听编报模式的SSE事件
  eventSource.addEventListener('thinking', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('thinking', data)
    } catch (error) {
      console.error('解析thinking事件数据失败:', error)
    }
  })

  eventSource.addEventListener('planning', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('planning', data)
    } catch (error) {
      console.error('解析planning事件数据失败:', error)
    }
  })

  eventSource.addEventListener('searching', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('searching', data)
    } catch (error) {
      console.error('解析searching事件数据失败:', error)
    }
  })

  eventSource.addEventListener('found_sources', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('found_sources', data)
    } catch (error) {
      console.error('解析found_sources事件数据失败:', error)
    }
  })

  eventSource.addEventListener('analyzing', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('analyzing', data)
    } catch (error) {
      console.error('解析analyzing事件数据失败:', error)
    }
  })

  eventSource.addEventListener('reflecting', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('reflecting', data)
    } catch (error) {
      console.error('解析reflecting事件数据失败:', error)
    }
  })

  eventSource.addEventListener('supplementing', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('supplementing', data)
    } catch (error) {
      console.error('解析supplementing事件数据失败:', error)
    }
  })

  eventSource.addEventListener('streaming', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('streaming', data)
    } catch (error) {
      console.error('解析streaming事件数据失败:', error)
    }
  })

  eventSource.addEventListener('completed', (event) => {
    try {
      const data = JSON.parse(event.data)
      onMessage && onMessage('completed', data)
      onComplete && onComplete(data)
    } catch (error) {
      console.error('解析completed事件数据失败:', error)
    }
  })

  // 监听连接打开事件
  eventSource.addEventListener('open', () => {
    console.log('编报模式SSE连接已建立')
  })

  // 监听错误事件
  eventSource.addEventListener('error', (event) => {
    console.error('编报模式SSE连接错误:', event)
    onError && onError(event)
  })

  return eventSource
}

/**
 * 关闭SSE连接
 * @param {Object} eventSource SSE连接对象
 */
export const closeSSEConnection = (eventSource) => {
  if (eventSource && eventSource.close) {
    eventSource.close()
    console.log('SSE连接已关闭')
  }
}
