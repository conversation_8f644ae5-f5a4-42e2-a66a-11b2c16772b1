import {request} from "@/utils/request.js"
import urls from "./urls.js"

export function knowledgeList() {
  return request({
    url: urls.knowledgeList,
    method: 'get',
  })
}

/**
 * 创建知识库
 * @param data
 * data: {
 *     name: string,        // 知识库名称（必填）
 *     description: string, // 知识库描述（可选）
 *     public: boolean      // 是否公开（默认false）
 * }
 */
export function createKnowledge(data) {
  return request({
    url: urls.createKnowledge,
    method: 'post',
    data
  })
}

/**
 * 文档列表
 * @param params
 * params: {
 *     page: 1,
 *     page_size: 10,
 *     collection_hash: xxxxxxxxxxx
 * }
 */
export function documentList(params) {
  return request({
    url: urls.documentList,
    method: 'get',
    params
  })
}
// 文档删除分类
export const deleteCategoryApi = (params)=>{
  return request({
    url:urls.deleteCategory,
    method:"delete",
    params
  })
}
// 文档删除
export const deleteCurrentDocument = (params)=>{
  return request({
    url:urls.deleteCurrentDocument,
    method:"delete",
    params
  })
}
/**
 * 上传文档到知识库
 * @param {FormData} formData - 包含文件和知识库哈希的表单数据
 * @param {Object} config - 上传配置选项
 * @returns {Promise} 上传结果
 */
export function uploadDocument(formData, config = {}) {
  return request({
    url: urls.uploadDocument,
    method: 'post',
    data: formData,
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    // 支持上传进度回调
    onUploadProgress: config.onProgress,
    // 上传超时时间设置为5分钟
    timeout: 300000,
    ...config
  })
}
