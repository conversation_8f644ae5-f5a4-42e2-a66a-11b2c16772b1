/**
 * 认证相关API
 */
// import http from '@/utils/request'
import urls from './urls'

/**
 * 用户登录
 * @param {Object} credentials 登录凭据
 * @param {string} credentials.username 用户名
 * @param {string} credentials.password 密码
 * @param {string} [credentials.captcha] 验证码
 * @param {string} [credentials.captchaId] 验证码ID
 * @returns {Promise} 登录结果
 */
export const login = (credentials) => {
  // return http.post(urls.login, {
  //   username: credentials.username,
  //   password: credentials.password,
  //   captcha: credentials.captcha,
  //   captcha_id: credentials.captchaId,
  //   // 添加设备信息
  //   device_info: {
  //     user_agent: navigator.userAgent,
  //     platform: navigator.platform,
  //     language: navigator.language,
  //     timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
  //   }
  // })
}

/**
 * 用户登出
 * @returns {Promise} 登出结果
 */
export const logout = () => {
  // return http.post(urls.logout)
}


/**
 * 刷新token
 * @param {string} refreshToken 刷新token
 * @returns {Promise} 新的token信息
 */
export const refreshToken = (refreshToken) => {
  // return http.post(urls.refreshToken, {
  //   refresh_token: refreshToken
  // })
}

/**
 * 获取图形验证码
 * @returns {Promise} 验证码信息
 */
export const getCaptcha = () => {
  // return http.get(urls.captcha)
}


// 导出所有认证API
export default {
  login,
  logout,
  refreshToken,
  getCaptcha,
}
