// 高端现代化知识库主题系统
// 基于 2024 年最新设计趋势，打造专业级视觉体验

// 浅色主题 - 极简现代风格
$light-theme: (
  // 主色调 - 高端紫蓝渐变系
  primary-color: #6366f1,        // 现代紫色，更具科技感
  primary-hover: #4f46e5,        // 深紫色悬停
  primary-active: #4338ca,       // 激活状态
  primary-light: #e0e7ff,        // 浅紫背景
  primary-lighter: #f0f4ff,      // 极浅紫背景
  primary-dark: #3730a3,         // 深紫色
  primary-gradient: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%), // 渐变色

  // 辅助色彩 - 现代化语义色
  success-color: #059669,        // 现代绿色
  success-light: #ecfdf5,
  success-gradient: linear-gradient(135deg, #059669 0%, #10b981 100%),
  warning-color: #d97706,        // 现代橙色
  warning-light: #fffbeb,
  error-color: #dc2626,          // 现代红色
  error-light: #fef2f2,
  info-color: #0891b2,           // 现代青色
  info-light: #f0fdfa,

  // 中性色系 - 高端灰色调（更细腻的层次）
  gray-25: #fcfcfd,              // 极浅灰
  gray-50: #f9fafb,              // 超浅灰
  gray-100: #f3f4f6,             // 浅灰
  gray-200: #e5e7eb,             // 边框灰
  gray-300: #d1d5db,             // 分割线灰
  gray-400: #9ca3af,             // 占位符灰
  gray-500: #6b7280,             // 次要文字灰
  gray-600: #4b5563,             // 常规文字灰
  gray-700: #374151,             // 主要文字灰
  gray-800: #1f2937,             // 深文字灰
  gray-900: #111827,             // 最深灰

  // 背景色系 - 层次分明
  bg-color: #ffffff,             // 纯白背景
  bg-color-page: #fafbfc,        // 页面背景（微灰）
  bg-color-container: #ffffff,    // 容器背景
  bg-color-elevated: #ffffff,     // 提升背景
  bg-color-overlay: rgba(255, 255, 255, 0.95), // 遮罩背景
  bg-color-mask: rgba(17, 24, 39, 0.5),        // 深色遮罩

  // 文字颜色 - 更好的可读性
  text-color-primary: #111827,   // 主文字（更深）
  text-color-regular: #374151,   // 常规文字
  text-color-secondary: #6b7280, // 次要文字
  text-color-placeholder: #9ca3af, // 占位符
  text-color-disabled: #d1d5db,  // 禁用文字

  // 边框颜色 - 精细化
  border-color: #e5e7eb,         // 主边框
  border-color-light: #f3f4f6,   // 浅边框
  border-color-lighter: #f9fafb, // 极浅边框
  border-color-dark: #d1d5db,    // 深边框

  // 填充色 - 现代化
  fill-color: #f3f4f6,           // 主填充
  fill-color-light: #f9fafb,     // 浅填充
  fill-color-lighter: #fcfcfd,   // 极浅填充
  fill-color-dark: #e5e7eb,      // 深填充

  // 现代阴影系统 - 更自然的投影
  box-shadow-xs: "0 1px 2px 0 rgba(0, 0, 0, 0.05)",
  box-shadow-sm: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
  box-shadow: "0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)",
  box-shadow-md: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)",
  box-shadow-lg: "0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04)",
  box-shadow-xl: "0 25px 50px -12px rgba(0, 0, 0, 0.25)",

  // Header 专用 - 现代化设计
  header-bg: rgba(255, 255, 255, 0.8),
  header-border: rgba(229, 231, 235, 0.6),
  header-text: #111827,
  header-icon: #6b7280,
  header-hover: rgba(99, 102, 241, 0.05),
  header-backdrop: blur(12px),   // 毛玻璃效果

  // 卡片 - 现代卡片设计
  card-bg: #ffffff,
  card-border: #f3f4f6,
  card-shadow: "0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06)",
  card-hover-shadow: "0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05)"
);

// 深色主题 - 高端暗黑风格
$dark-theme: (
  // 主色调 - 更亮的紫蓝色系（暗色下更突出）
  primary-color: #8b5cf6,        // 亮紫色，在暗色下更显眼
  primary-hover: #7c3aed,        // 深紫悬停
  primary-active: #6d28d9,       // 激活状态
  primary-light: rgba(139, 92, 246, 0.15), // 紫色背景
  primary-lighter: rgba(139, 92, 246, 0.08), // 极浅紫背景
  primary-dark: #5b21b6,         // 深紫
  primary-gradient: linear-gradient(135deg, #8b5cf6 0%, #a855f7 100%), // 暗色渐变

  // 辅助色彩 - 暗色优化的现代色
  success-color: #10b981,        // 明亮绿色
  success-light: rgba(16, 185, 129, 0.15),
  success-gradient: linear-gradient(135deg, #10b981 0%, #34d399 100%),
  warning-color: #f59e0b,        // 明亮橙色
  warning-light: rgba(245, 158, 11, 0.15),
  error-color: #f87171,          // 明亮红色
  error-light: rgba(248, 113, 113, 0.15),
  info-color: #22d3ee,           // 明亮青色
  info-light: rgba(34, 211, 238, 0.15),

  // 中性色系 - 高端暗色调（更丰富的层次）
  gray-25: #0a0e1a,              // 最深背景
  gray-50: #0f172a,              // 深背景
  gray-100: #1e293b,             // 容器背景
  gray-200: #334155,             // 边框色
  gray-300: #475569,             // 分割线
  gray-400: #64748b,             // 占位符
  gray-500: #94a3b8,             // 次要文字
  gray-600: #cbd5e1,             // 常规文字
  gray-700: #e2e8f0,             // 主要文字
  gray-800: #f1f5f9,             // 高亮文字
  gray-900: #f8fafc,             // 最亮文字

  // 背景色系 - 深邃层次
  bg-color: #0a0e1a,             // 最深背景
  bg-color-page: #0f172a,        // 页面背景
  bg-color-container: #1e293b,    // 容器背景
  bg-color-elevated: #334155,     // 提升背景
  bg-color-overlay: rgba(10, 14, 26, 0.95), // 遮罩背景
  bg-color-mask: rgba(0, 0, 0, 0.8),        // 深色遮罩

  // 文字颜色 - 暗色下的高对比度
  text-color-primary: #f8fafc,   // 主文字（最亮）
  text-color-regular: #e2e8f0,   // 常规文字
  text-color-secondary: #cbd5e1, // 次要文字
  text-color-placeholder: #64748b, // 占位符
  text-color-disabled: #475569,  // 禁用文字

  // 边框颜色 - 暗色精细化
  border-color: #334155,         // 主边框
  border-color-light: #1e293b,   // 浅边框
  border-color-lighter: #0f172a, // 极浅边框
  border-color-dark: #475569,    // 深边框

  // 填充色 - 暗色层次
  fill-color: #1e293b,           // 主填充
  fill-color-light: #0f172a,     // 浅填充
  fill-color-lighter: #0a0e1a,   // 极浅填充
  fill-color-dark: #334155,      // 深填充

  // 现代阴影系统 - 暗色下的深邃阴影
  box-shadow-xs: "0 1px 2px 0 rgba(0, 0, 0, 0.3)",
  box-shadow-sm: "0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3)",
  box-shadow: "0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4)",
  box-shadow-md: "0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4)",
  box-shadow-lg: "0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5)",
  box-shadow-xl: "0 25px 50px -12px rgba(0, 0, 0, 0.8)",

  // Header 专用 - 暗色现代化
  header-bg: rgba(10, 14, 26, 0.8),
  header-border: rgba(51, 65, 85, 0.6),
  header-text: #f8fafc,
  header-icon: #cbd5e1,
  header-hover: rgba(139, 92, 246, 0.1),
  header-backdrop: blur(12px),   // 毛玻璃效果

  // 卡片 - 暗色现代卡片
  card-bg: #1e293b,
  card-border: #334155,
  card-shadow: "0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3)",
  card-hover-shadow: "0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4)"
);

// 生成CSS自定义属性
:root {
  @each $key, $value in $light-theme {
    --#{$key}: #{$value};
  }
}

[data-theme="dark"] {
  @each $key, $value in $dark-theme {
    --#{$key}: #{$value};
  }
}

// 现代化主题切换动画 - 更流畅的过渡
* {
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// 全局样式重置 - 现代化基础
body {
  background-color: var(--bg-color-page);
  color: var(--text-color-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin: 0;
}

// Element Plus 组件现代化适配
.el-container {
  background-color: var(--bg-color-page);
}

.el-header {
  background: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  color: var(--header-text);
  backdrop-filter: var(--header-backdrop);
  -webkit-backdrop-filter: var(--header-backdrop);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(99, 102, 241, 0.02) 50%, transparent 100%);
    pointer-events: none;
  }
}

.el-main {
  background-color: var(--bg-color-page);
  color: var(--text-color-primary);
}

// 现代化卡片设计
.el-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-2px);
    border-color: var(--primary-color);
  }

  .el-card__header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    padding: 20px 24px;
    border-bottom: none;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 18px;
        font-weight: 600;
        letter-spacing: -0.025em;
      }
    }
  }

  .el-card__body {
    padding: 24px;
  }
}

// 现代化按钮设计
.el-button {
  border-radius: 8px;
  font-weight: 500;
  letter-spacing: -0.025em;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.el-button--primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.25);

    &:hover {
      background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
      box-shadow: 0 6px 20px 0 rgba(99, 102, 241, 0.35);
      transform: translateY(-1px);
    }

    &:active {
      transform: translateY(0);
      box-shadow: 0 2px 8px 0 rgba(99, 102, 241, 0.3);
    }
  }
}

// 现代化标签设计
.el-tag {
  border-radius: 6px;
  font-weight: 500;
  font-size: 12px;
  letter-spacing: 0.025em;
  text-transform: uppercase;

  &.el-tag--primary {
    background: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }

  &.el-tag--info {
    background: var(--fill-color);
    color: var(--text-color-secondary);
    border-color: var(--border-color);
  }
}

// 高端主题切换按钮
.theme-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 44px;
  height: 44px;
  border-radius: 12px;
  background: var(--fill-color);
  border: 1px solid var(--border-color);
  color: var(--header-icon);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  &:hover {
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(99, 102, 241, 0.25);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(-1px);
  }

  .el-icon {
    font-size: 20px;
    position: relative;
    z-index: 1;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  &:hover .el-icon {
    transform: rotate(180deg) scale(1.1);
  }
}
