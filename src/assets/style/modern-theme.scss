// 高端现代化知识库主题系统 - 简化版本
// 基于 2024 年最新设计趋势，打造专业级视觉体验

// 浅色主题 CSS 变量
:root {
  // 主色调 - 高端紫蓝渐变系
  --primary-color: #6366f1;
  --primary-hover: #4f46e5;
  --primary-active: #4338ca;
  --primary-light: #e0e7ff;
  --primary-lighter: #f0f4ff;
  --primary-dark: #3730a3;

  // 辅助色彩 - 现代化语义色
  --success-color: #059669;
  --success-light: #ecfdf5;
  --warning-color: #d97706;
  --warning-light: #fffbeb;
  --error-color: #dc2626;
  --error-light: #fef2f2;
  --info-color: #0891b2;
  --info-light: #f0fdfa;

  // 中性色系 - 高端灰色调
  --gray-25: #fcfcfd;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  // 背景色系
  --bg-color: #ffffff;
  --bg-color-page: #fafbfc;
  --bg-color-container: #ffffff;
  --bg-color-elevated: #ffffff;
  --bg-color-overlay: rgba(255, 255, 255, 0.95);
  --bg-color-mask: rgba(17, 24, 39, 0.5);

  // 文字颜色
  --text-color-primary: #111827;
  --text-color-regular: #374151;
  --text-color-secondary: #6b7280;
  --text-color-placeholder: #9ca3af;
  --text-color-disabled: #d1d5db;

  // 边框颜色
  --border-color: #e5e7eb;
  --border-color-light: #f3f4f6;
  --border-color-lighter: #f9fafb;
  --border-color-dark: #d1d5db;

  // 填充色
  --fill-color: #f3f4f6;
  --fill-color-light: #f9fafb;
  --fill-color-lighter: #4e4e81;
  --fill-color-dark: #e5e7eb;

  // 现代阴影系统
  --box-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --box-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --box-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);

  // Header 专用
  --header-bg: rgba(255, 255, 255, 0.8);
  --header-border: rgba(229, 231, 235, 0.6);
  --header-text: #111827;
  --header-icon: #6b7280;
  --header-hover: rgba(99, 102, 241, 0.05);

  // 卡片
  --card-bg: #ffffff;
  --card-border: #f3f4f6;
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --card-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

// 深色主题
[data-theme="dark"] {
  // 主色调 - 更亮的紫蓝色系
  --primary-color: #8b5cf6;
  --primary-hover: #7c3aed;
  --primary-active: #6d28d9;
  --primary-light: rgba(139, 92, 246, 0.15);
  --primary-lighter: rgba(139, 92, 246, 0.08);
  --primary-dark: #5b21b6;

  // 辅助色彩 - 暗色优化
  --success-color: #10b981;
  --success-light: rgba(16, 185, 129, 0.15);
  --warning-color: #f59e0b;
  --warning-light: rgba(245, 158, 11, 0.15);
  --error-color: #f87171;
  --error-light: rgba(248, 113, 113, 0.15);
  --info-color: #22d3ee;
  --info-light: rgba(34, 211, 238, 0.15);

  // 中性色系 - 暗色调
  --gray-25: #0a0e1a;
  --gray-50: #0f172a;
  --gray-100: #1e293b;
  --gray-200: #334155;
  --gray-300: #475569;
  --gray-400: #64748b;
  --gray-500: #94a3b8;
  --gray-600: #cbd5e1;
  --gray-700: #e2e8f0;
  --gray-800: #f1f5f9;
  --gray-900: #f8fafc;

  // 背景色系
  --bg-color: #0a0e1a;
  --bg-color-page: #0f172a;
  --bg-color-container: #1e293b;
  --bg-color-elevated: #334155;
  --bg-color-overlay: rgba(10, 14, 26, 0.95);
  --bg-color-mask: rgba(0, 0, 0, 0.8);

  // 文字颜色
  --text-color-primary: #f8fafc;
  --text-color-regular: #e2e8f0;
  --text-color-secondary: #cbd5e1;
  --text-color-placeholder: #64748b;
  --text-color-disabled: #475569;

  // 边框颜色
  --border-color: #334155;
  --border-color-light: #1e293b;
  --border-color-lighter: #0f172a;
  --border-color-dark: #475569;

  // 填充色
  --fill-color: #1e293b;
  --fill-color-light: #0f172a;
  --fill-color-lighter: #0a0e1a;
  --fill-color-dark: #334155;

  // 现代阴影系统 - 暗色
  --box-shadow-xs: 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --box-shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.5), 0 2px 4px -1px rgba(0, 0, 0, 0.4);
  --box-shadow-md: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
  --box-shadow-lg: 0 20px 25px -5px rgba(0, 0, 0, 0.6), 0 10px 10px -5px rgba(0, 0, 0, 0.5);
  --box-shadow-xl: 0 25px 50px -12px rgba(0, 0, 0, 0.8);

  // Header 专用 - 暗色
  --header-bg: rgba(10, 14, 26, 0.8);
  --header-border: rgba(51, 65, 85, 0.6);
  --header-text: #f8fafc;
  --header-icon: #cbd5e1;
  --header-hover: rgba(139, 92, 246, 0.1);

  // 卡片 - 暗色
  --card-bg: #1e293b;
  --card-border: #334155;
  --card-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.4), 0 1px 2px 0 rgba(0, 0, 0, 0.3);
  --card-hover-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.5), 0 4px 6px -2px rgba(0, 0, 0, 0.4);
}

// 现代化主题切换动画
* {
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              border-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              box-shadow 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

// 全局样式重置
body {
  background-color: var(--bg-color-page);
  color: var(--text-color-primary);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue', sans-serif;
  font-feature-settings: 'kern' 1, 'liga' 1, 'calt' 1;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  transition: background-color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94),
              color 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  margin: 0;
}

// Element Plus 组件现代化适配
.el-container {
  background-color: var(--bg-color-page);
}

.el-header {
  background: var(--header-bg);
  border-bottom: 1px solid var(--header-border);
  color: var(--header-text);
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(99, 102, 241, 0.02) 50%, transparent 100%);
    pointer-events: none;
  }
}

.el-main {
  background-color: var(--bg-color-page);
  color: var(--text-color-primary);
}

// 现代化卡片设计
.el-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 16px;
  box-shadow: var(--card-shadow);
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &:hover {
    box-shadow: var(--card-hover-shadow);
    transform: translateY(-4px);
    border-color: var(--primary-color);
  }

  .el-card__header {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    color: white;
    padding: 24px 28px;
    border-bottom: none;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      span {
        font-size: 20px;
        font-weight: 700;
        letter-spacing: -0.025em;
      }
    }
  }

  .el-card__body {
    padding: 28px;
  }
}

// 现代化按钮设计
.el-button {
  border-radius: 12px;
  font-weight: 600;
  letter-spacing: -0.025em;
  transition: all 0.2s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &.el-button--primary {
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    border: none;
    box-shadow: 0 4px 14px 0 rgba(99, 102, 241, 0.25);

    &:hover {
      background: linear-gradient(135deg, var(--primary-hover) 0%, var(--primary-active) 100%);
      box-shadow: 0 8px 25px 0 rgba(99, 102, 241, 0.35);
      transform: translateY(-2px);
    }

    &:active {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px 0 rgba(99, 102, 241, 0.3);
    }
  }
}

// 现代化标签设计
.el-tag {
  border-radius: 8px;
  font-weight: 600;
  font-size: 12px;
  letter-spacing: 0.025em;
  text-transform: uppercase;

  &.el-tag--primary {
    background: var(--primary-light);
    color: var(--primary-color);
    border-color: var(--primary-color);
  }

  &.el-tag--info {
    background: var(--fill-color);
    color: var(--text-color-secondary);
    border-color: var(--border-color);
  }
}

// 高端主题切换按钮
.theme-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  border-radius: 16px;
  background: var(--fill-color);
  border: 1px solid var(--border-color);
  color: var(--header-icon);
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, var(--primary-color) 0%, var(--primary-hover) 100%);
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  &:hover {
    color: white;
    border-color: var(--primary-color);
    transform: translateY(-3px);
    box-shadow: 0 12px 30px rgba(99, 102, 241, 0.3);

    &::before {
      opacity: 1;
    }
  }

  &:active {
    transform: translateY(-2px);
  }

  .el-icon {
    font-size: 22px;
    position: relative;
    z-index: 1;
    transition: transform 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  &:hover .el-icon {
    transform: rotate(180deg) scale(1.1);
  }
}
