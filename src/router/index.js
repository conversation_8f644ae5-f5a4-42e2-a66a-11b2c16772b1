import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'
import { ElMessage } from 'element-plus'

// 路由组件懒加载 - 按功能模块组织
// 通用页面 (common)
const Home = () => import('@/views/common/Home.vue')
const Search = () => import('@/views/common/Search.vue')
const NotFound = () => import('@/views/common/NotFound.vue')

// 认证相关页面 (auth)
const Login = () => import('@/views/auth/Login.vue')

// 知识管理相关页面 (knowledge)
const KnowledgeManagement = () => import('@/views/knowledge/KnowledgeManagement.vue')

// 用户相关页面 (user)
const Settings = () => import('@/views/user/Settings.vue')

// 路由配置
const routes = [
  {
    path: '/',
    name: 'Home',
    component: Home,
    meta: {
      title: '首页',
      icon: 'House',
      requiresAuth: true,
      keepAlive: true
    }
  },
  {
    path: '/login',
    name: 'Login',
    component: Login,
    meta: {
      title: '用户登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/knowledge',
    name: 'KnowledgeManagement',
    component: KnowledgeManagement,
    meta: {
      title: '知识管理',
      icon: 'FolderOpened',
      requiresAuth: true,
      keepAlive: true
    }
  },
  {
    path: '/search',
    name: 'Search',
    component: Search,
    meta: {
      title: '搜索',
      icon: 'Search',
      requiresAuth: false,
      keepAlive: false
    }
  },
  {
    path: '/settings',
    name: 'Settings',
    component: Settings,
    meta: {
      title: '设置',
      icon: 'Setting',
      requiresAuth: true,
      keepAlive: true
    }
  },
  // 重定向路由
  {
    path: '/index',
    redirect: '/'
  },
  // 404页面
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: NotFound,
    meta: {
      title: '页面未找到',
      requiresAuth: false
    }
  }
]

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes,
  // 路由切换时的滚动行为
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局前置守卫
router.beforeEach(async (to, from, next) => {
  // 设置页面标题
  const title = to.meta.title || '知识库系统'
  document.title = `${title} - 知识库系统`

  // 路由切换时的加载状态
  const loadingElement = document.getElementById('route-loading')
  if (loadingElement) {
    loadingElement.style.display = 'block'
  }

  // 获取用户store
  const userStore = useUserStore()

  // 检查自动登录（仅在非登录页面时检查）
  if (to.path !== '/login' && !userStore.isLoggedIn) {
    userStore.checkAutoLogin()
  }

  // 权限验证逻辑
  if (to.meta.requiresAuth) {
    if (!userStore.isLoggedIn) {
      // 未登录，重定向到登录页
      // 只在非登录页面时显示提示
      if (from.path !== '/login') {
        ElMessage.warning('请先登录')
      }
      next({
        path: '/login',
        query: to.fullPath !== '/' ? { redirect: to.fullPath } : {}
      })
      return
    }
  }

  // 如果已登录用户访问登录页，重定向到首页或原来要访问的页面
  if (to.path === '/login' && userStore.isLoggedIn) {
    const redirect = to.query.redirect || '/'
    next(redirect)
    return
  }

  next()
})

// 全局后置钩子
router.afterEach((to, from) => {
  // 隐藏加载状态
  const loadingElement = document.getElementById('route-loading')
  if (loadingElement) {
    setTimeout(() => {
      loadingElement.style.display = 'none'
    }, 200)
  }

  // 路由切换完成后的处理
  console.log(`路由切换: ${from.path} -> ${to.path}`)
})

export default router
