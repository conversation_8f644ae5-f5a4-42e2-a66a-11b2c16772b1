<script setup>
import { onMounted, onUnmounted, computed } from 'vue'
import { useRoute } from 'vue-router'
import { useThemeStore } from '@/stores/theme'
import { useUserStore } from '@/stores/user'
import AppHeader from '@/components/AppHeader.vue'
import { Loading } from '@element-plus/icons-vue'

// 路由和状态管理
const route = useRoute()
const themeStore = useThemeStore()
const userStore = useUserStore()

// 判断是否为认证页面（登录页等）
const isAuthPage = computed(() => {
  const authRoutes = ['/login', '/register', '/forgot-password']
  return authRoutes.includes(route.path)
})

// 获取需要缓存的组件列表
const getKeepAliveComponents = () => {
  // 返回需要缓存的组件名称数组
  return ['Home', 'KnowledgeManagement', 'Search', 'Settings']
}

// 主题清理函数
// let themeCleanup = null

onMounted(() => {
  // 初始化主题
  themeStore.initTheme()

  // 应用主题到document
  // const applyTheme = () => {
  //   document.documentElement.setAttribute('data-theme', themeStore.currentTheme)
  // }

  // applyTheme()

  // 检查自动登录
  // userStore.checkAutoLogin()
})

onUnmounted(() => {
  // 清理主题监听器
  if (themeCleanup) {
    themeCleanup()
  }
})
</script>

<template>
<!--  <div id="app" :class="{ 'dark-theme': themeStore.isDark }">-->
<!--    -->
<!--  </div>-->
  <!-- 路由加载指示器 -->
  <div id="route-loading" class="route-loading" style="display: none;">
    <div class="loading-content">
      <el-icon class="is-loading"><Loading /></el-icon>
      <span>页面加载中...</span>
    </div>
  </div>

  <!-- 根据路由和登录状态动态显示不同布局 -->
  <div v-if="isAuthPage" class="auth-layout">
    <!-- 认证页面布局（登录页等）- 无Header -->
    <router-view v-slot="{ Component, route }">
      <transition name="fade" mode="out-in">
        <component :is="Component" :key="route.path" />
      </transition>
    </router-view>
  </div>

  <div v-else class="main-layout">
    <!-- 主应用布局 - 包含Header -->
    <el-container class="app-container" direction="vertical">
      <!-- 头部导航 -->
      <AppHeader />

      <!-- 主要内容区域 -->
      <el-main class="app-main">
        <!-- 路由视图 -->
        <router-view v-slot="{ Component, route }">
          <transition name="fade" mode="out-in">
            <keep-alive :include="getKeepAliveComponents()">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </el-main>
    </el-container>
  </div>
</template>



<style lang="scss">
// 导入现代化主题样式
@use '@/assets/style/theme.scss' as *;

#app {

  overflow: hidden;

  &.dark-theme {
    color-scheme: dark;
  }
}

// 认证页面布局（登录页等）
.auth-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;
  background: var(--bg-color-page);
}

// 主应用布局
.main-layout {
  height: 100vh;
  width: 100vw;
  overflow: hidden;

  .app-container {
    height: 100vh;
    background-color: var(--bg-color-page);
  }
}



.app-main {
  background-color: var(--bg-color-page);
  overflow-y: auto;
  height: calc(100vh - 60px);
  position: relative;
}

// 路由加载指示器
.route-loading {
  position: fixed;
  top: 60px;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-color) 0%, var(--primary-hover) 100%);
  z-index: 9999;
  animation: loading-progress 1s ease-in-out infinite;

  .loading-content {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-color-overlay);
    padding: 12px 24px;
    border-radius: 8px;
    box-shadow: var(--box-shadow);
    color: var(--text-color-primary);
    font-size: 14px;

    .el-icon {
      font-size: 16px;
      color: var(--primary-color);
    }
  }
}

// 路由过渡动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 加载动画
@keyframes loading-progress {
  0% {
    transform: translateX(-100%);
  }
  50% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(100%);
  }
}
</style>
