{"name": "knowledg_web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.10.0", "element-plus": "^2.10.4", "markdown-it": "^14.1.0", "pinia": "^3.0.3", "sass": "^1.89.2", "sse.js": "^2.6.0", "uuid": "^11.1.0", "vue": "^3.5.17", "vue-element-plus-x": "^1.3.0", "vue-router": "^4.5.1"}, "devDependencies": {"@vitejs/plugin-vue": "^6.0.0", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.4"}}